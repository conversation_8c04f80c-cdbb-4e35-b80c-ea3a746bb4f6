from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlmodel import Session, select
import json
import traceback
import logging

from app.database import get_session
from app.models.db_models import MccAnalysis, WebsiteUrls, ScrapeRequestTracker
from app.models.request_models import MccAnalysisRequest
# REMOVED: from app.tasks.celery_tasks import process_mcc_analysis
from app.utils.website_url_processor import store_urls_from_request, get_urls_by_scrape_ref

router = APIRouter()
logger = logging.getLogger(__name__)


# Define the background task function (mimics the old Celery task)
def run_mcc_analysis_task(analysis_id: int):
    from app.services.mcc_service import MccClassificationService
    from sqlmodel import Session
    from app.models.db_models import MccAnalysis
    from app.database import engine
    import logging
    import traceback
    from datetime import datetime
    logger = logging.getLogger(__name__)

    # Log task start
    logger.info(f"=== BACKGROUND TASK STARTED for MCC analysis ID: {analysis_id} ===")

    try:
        # Update status to PROCESSING
        with Session(engine) as session:
            analysis = session.get(MccAnalysis, analysis_id)
            if not analysis:
                logger.error(f"MccAnalysis with ID {analysis_id} not found")
                return
            scrape_request_ref_id = analysis.scrape_request_ref_id
            org_id = analysis.org_id
            website = analysis.website
            analysis.processing_status = "PROCESSING"
            analysis.started_at = datetime.now().isoformat() + "Z"
            session.commit()
            logger.info(f"Updated MccAnalysis {analysis_id} status to PROCESSING")

        # Process MCC analysis
        logger.info(f"Starting MCC classification service for analysis {analysis_id}")
        mcc_service = MccClassificationService(scrape_request_ref_id=scrape_request_ref_id, org_id=org_id)
        result = mcc_service.process_mcc_analysis()
        logger.info(f"MCC analysis task {analysis_id} completed with status: {result.get('status')}")

        # Update the analysis status with result
        with Session(engine) as session:
            analysis = session.get(MccAnalysis, analysis_id)
            if analysis:
                analysis.processing_status = result.get('status', 'COMPLETED')
                analysis.completed_at = datetime.now().isoformat() + "Z"
                session.commit()
                logger.info(f"Updated MccAnalysis {analysis_id} final status to: {analysis.processing_status}")

        logger.info(f"=== BACKGROUND TASK COMPLETED SUCCESSFULLY for analysis ID: {analysis_id} ===")

    except Exception as e:
        logger.error(f"=== BACKGROUND TASK FAILED for analysis ID: {analysis_id} ===")
        logger.error(f"Error details: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")

        # Update status to FAILED in database
        try:
            with Session(engine) as session:
                analysis = session.get(MccAnalysis, analysis_id)
                if analysis:
                    analysis.processing_status = "FAILED"
                    analysis.completed_at = datetime.now().isoformat() + "Z"
                    session.commit()
                    logger.info(f"Updated MccAnalysis {analysis_id} status to FAILED")
        except Exception as db_error:
            logger.error(f"Failed to update database status to FAILED: {str(db_error)}")

@router.post("/", status_code=200)
def create_or_get_mcc_analysis(
    request: MccAnalysisRequest, 
    background_tasks: BackgroundTasks,
    session: Session = Depends(get_session)
):
    """
    Create a new MCC analysis task or get existing one with comprehensive error handling
    
    This endpoint:
    1. Checks if an analysis with the same scrape request ID exists
    2. Creates a new MCC analysis record if needed
    3. Stores website URLs in the database
    4. Triggers a background task for asynchronous processing
    5. Returns immediately with 200 status after task creation
    """
    try:
        # Validate request
        if not request or not hasattr(request, 'scrapeRequestRefID') or not request.scrapeRequestRefID:
            logger.error("Invalid request: missing scrapeRequestRefID")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid request: scrapeRequestRefID is required"
            )
        
        if not hasattr(request, 'website') or not request.website:
            logger.error("Invalid request: missing website")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid request: website is required"
            )
        
        logger.info(f"Processing MCC analysis request for {request.website} with ref_id {request.scrapeRequestRefID}")
        
        # Check if an MccAnalysis with the same scrapeRequestRefID exists
        try:
            statement = select(MccAnalysis).where(
                MccAnalysis.scrape_request_ref_id == request.scrapeRequestRefID
            )
            existing_analysis = session.exec(statement).first()
            
            if existing_analysis:
                logger.info(f"MCC analysis already exists with ID {existing_analysis.id}")
                return {
                    "message": "MccAnalysis already exists.", 
                    "id": existing_analysis.id,
                    "status": getattr(existing_analysis, 'processing_status', 'UNKNOWN')
                }
        except Exception as db_error:
            logger.error(f"Database error checking existing analysis: {str(db_error)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Database error while checking existing analysis"
            )

        # Store URLs from the request first
        try:
            url_store_result = store_urls_from_request(request, session, auto_classify=False)
            if url_store_result != 1:  # 1 indicates success, -1 indicates failure
                logger.error("Failed to store URLs")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to store website URLs"
                )
        except Exception as url_store_error:
            logger.error(f"Error storing URLs: {str(url_store_error)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error storing URLs: {str(url_store_error)}"
            )
        
        # Create a new MccAnalysis from the request (no website_id needed)
        try:
            new_analysis = MccAnalysis(
                website=request.website,
                scrape_request_ref_id=request.scrapeRequestRefID,
                org_id=getattr(request, 'org_id', None) or "default",
                processing_status="PENDING"
            )
            session.add(new_analysis)
            session.commit()
            session.refresh(new_analysis)  # Refresh to get the auto-generated ID
            
            logger.info(f"Created new MCC analysis with ID {new_analysis.id}")
            
        except Exception as create_error:
            session.rollback()
            logger.error(f"Error creating MCC analysis record: {str(create_error)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error creating MCC analysis record: {str(create_error)}"
            )
        
        # Trigger background task for processing
        try:
            logger.info(f"=== QUEUEING BACKGROUND TASK for analysis {new_analysis.id} ===")
            background_tasks.add_task(run_mcc_analysis_task, new_analysis.id)
            logger.info(f"Background task successfully queued for analysis {new_analysis.id}")
            logger.info(f"Task will process: website={new_analysis.website}, scrape_ref={new_analysis.scrape_request_ref_id}")
        except Exception as task_error:
            logger.error(f"=== FAILED TO QUEUE BACKGROUND TASK for analysis {new_analysis.id} ===")
            logger.error(f"Error triggering background task: {str(task_error)}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            # Don't fail the request if background task fails to start
            # The task can be retried later

        return {
            "message": "MccAnalysis created successfully and processing started.", 
            "id": new_analysis.id,
            "status": "PENDING",
            "task_id": None
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in create_or_get_mcc_analysis: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )



@router.get("/by-request/{scrape_request_ref_id}")
def get_mcc_analysis_by_request_id(
    scrape_request_ref_id: str, 
    session: Session = Depends(get_session)
):
    """
    Get MCC analysis by scrape request reference ID
    """
    try:
        analysis = session.exec(
            select(MccAnalysis).where(
                MccAnalysis.scrape_request_ref_id == scrape_request_ref_id
            )
        ).first()
        
        if not analysis:
            raise HTTPException(
                status_code=404, 
                detail=f"MccAnalysis not found for request ID: {scrape_request_ref_id}"
            )
        
        # Parse details JSON if available
        details = {}
        if hasattr(analysis, 'details') and analysis.details:
            try:
                details = json.loads(analysis.details)
            except json.JSONDecodeError:
                details = {"error": "Invalid JSON in details field"}
        
        # Get MCC code
        mcc_code = analysis.mcc_code if hasattr(analysis, 'mcc_code') else None
        
        # Prepare response with details
        response = {
            "id": analysis.id,
            "website": analysis.website,
            "scrape_request_ref_id": analysis.scrape_request_ref_id,
            "mcc_code": mcc_code,
            "business_description": analysis.business_description if hasattr(analysis, 'business_description') else "",
            "reasoning": analysis.reasoning if hasattr(analysis, 'reasoning') else "",
            "processing_status": analysis.processing_status if hasattr(analysis, 'processing_status') else "UNKNOWN",
            "created_at": analysis.created_at if hasattr(analysis, 'created_at') else None,
            "started_at": analysis.started_at if hasattr(analysis, 'started_at') else None,
            "completed_at": analysis.completed_at if hasattr(analysis, 'completed_at') else None
        }
        
        # Add details only if they exist
        if details:
            response["details"] = details
            
            # Extract flow indicators from details to top level for easier access
            if isinstance(details, dict):
                flow_indicators = [
                    "analysis_flow_type", "content_availability_status", 
                    "fallback_method_used", "text_extraction_used", "insufficient_data"
                ]
                for indicator in flow_indicators:
                    if indicator in details:
                        response[indicator] = details[indicator]
        
        return response
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting MCC analysis by request ID {scrape_request_ref_id}: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting MCC analysis: {str(e)}"
        )



