nohup: ignoring input
2025-07-29 19:31:24,241 - app.main - INFO - Request logging middleware initialized and ready to capture requests
INFO:     Started server process [32858]
INFO:     Waiting for application startup.
2025-07-29 19:31:24,245 - app.main - INFO - Initializing application
2025-07-29 19:31:24,246 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 19:31:24,246 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-29 19:31:24,246 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("mcc_analysis_gemini")
2025-07-29 19:31:24,246 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("mcc_analysis_gemini")
2025-07-29 19:31:24,246 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 19:31:24,246 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-29 19:31:24,247 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("website_urls_gemini")
2025-07-29 19:31:24,247 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("website_urls_gemini")
2025-07-29 19:31:24,247 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 19:31:24,247 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-29 19:31:24,247 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("general_logs_gemini")
2025-07-29 19:31:24,247 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("general_logs_gemini")
2025-07-29 19:31:24,247 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 19:31:24,247 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-29 19:31:24,247 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("gemini_api_log_gemini")
2025-07-29 19:31:24,247 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("gemini_api_log_gemini")
2025-07-29 19:31:24,247 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 19:31:24,247 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-29 19:31:24,247 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("scrape_request_tracker_gemini")
2025-07-29 19:31:24,247 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("scrape_request_tracker_gemini")
2025-07-29 19:31:24,247 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 19:31:24,247 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-29 19:31:24,247 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("scraped_urls_gemini")
2025-07-29 19:31:24,247 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("scraped_urls_gemini")
2025-07-29 19:31:24,247 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 19:31:24,247 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-29 19:31:24,247 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("websites_gemini")
2025-07-29 19:31:24,247 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("websites_gemini")
2025-07-29 19:31:24,247 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 19:31:24,247 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-29 19:31:24,248 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("mcc_url_classification_gemini")
2025-07-29 19:31:24,248 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("mcc_url_classification_gemini")
2025-07-29 19:31:24,248 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 19:31:24,248 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-29 19:31:24,248 INFO sqlalchemy.engine.Engine COMMIT
2025-07-29 19:31:24,248 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-29 19:31:24,248 - app.main - INFO - Database initialized successfully
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
[2025-07-29 19:31:43][request_middleware][NO_REF] INFO: Request started: GET /docs
{
  "request_id": "aa270ddf-9fd8-4103-a1d0-2ac8b253b214",
  "client_ip": "127.0.0.1",
  "user_agent": "Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0",
  "query_params": null,
  "headers": {
    "host": "127.0.0.1:8000",
    "user-agent": "Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0",
    "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "accept-language": "en-US,en;q=0.5",
    "accept-encoding": "gzip, deflate, br, zstd",
    "connection": "keep-alive",
    "cookie": "username-127-0-0-1-8888=\"2|1:0|10:1753687981|23:username-127-0-0-1-8888|44:OTI1NWYwNDA2MmYzNGM4ODhmZWEyMzU0NDNhZWYzZGU=|af37f3c5748e269f0b3a691141a8d9d34952092469c0df3eaa2fabbf0681f48d\"; _xsrf=2|a122885f|aa0d74d2af0b3aa32b5dde71a04b1553|1753687981",
    "upgrade-insecure-requests": "1",
    "sec-fetch-dest": "document",
    "sec-fetch-mode": "navigate",
    "sec-fetch-site": "none",
    "sec-fetch-user": "?1",
    "priority": "u=0, i"
  },
  "url_full": "http://127.0.0.1:8000/docs"
}
[2025-07-29 19:31:43][request_middleware][NO_REF] INFO: Request completed: GET /docs - 200
{
  "request_id": "aa270ddf-9fd8-4103-a1d0-2ac8b253b214",
  "status_code": 200,
  "response_time_ms": 0.59
}
INFO:     127.0.0.1:49992 - "GET /docs HTTP/1.1" 200 OK
[2025-07-29 19:31:43][request_middleware][NO_REF] INFO: Request started: GET /openapi.json
{
  "request_id": "5cba9d6c-7955-4da8-9759-593d58af8398",
  "client_ip": "127.0.0.1",
  "user_agent": "Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0",
  "query_params": null,
  "headers": {
    "host": "127.0.0.1:8000",
    "user-agent": "Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0",
    "accept": "application/json,*/*",
    "accept-language": "en-US,en;q=0.5",
    "accept-encoding": "gzip, deflate, br, zstd",
    "referer": "http://127.0.0.1:8000/docs",
    "connection": "keep-alive",
    "cookie": "username-127-0-0-1-8888=\"2|1:0|10:1753687981|23:username-127-0-0-1-8888|44:OTI1NWYwNDA2MmYzNGM4ODhmZWEyMzU0NDNhZWYzZGU=|af37f3c5748e269f0b3a691141a8d9d34952092469c0df3eaa2fabbf0681f48d\"; _xsrf=2|a122885f|aa0d74d2af0b3aa32b5dde71a04b1553|1753687981",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "priority": "u=4"
  },
  "url_full": "http://127.0.0.1:8000/openapi.json"
}
[2025-07-29 19:31:43][request_middleware][NO_REF] INFO: Request completed: GET /openapi.json - 200
{
  "request_id": "5cba9d6c-7955-4da8-9759-593d58af8398",
  "status_code": 200,
  "response_time_ms": 6.6
}
INFO:     127.0.0.1:49992 - "GET /openapi.json HTTP/1.1" 200 OK
2025-07-29 19:31:49,995 - app.routers.mcc_analysis - INFO - Processing MCC analysis request for https://drynotch.com with ref_id 208378df-cfasdf32-4b91-aaea-f15f9cfeddcd
[2025-07-29 19:31:49][request_middleware][NO_REF] INFO: Request started: POST /mcc-analysis/
{
  "request_id": "704d006a-63fc-408a-a350-ba14d484ee3b",
  "client_ip": "127.0.0.1",
  "user_agent": "Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0",
  "query_params": null,
  "headers": {
    "host": "127.0.0.1:8000",
    "user-agent": "Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0",
    "accept": "application/json",
    "accept-language": "en-US,en;q=0.5",
    "accept-encoding": "gzip, deflate, br, zstd",
    "referer": "http://127.0.0.1:8000/docs",
    "content-type": "application/json",
    "content-length": "3191",
    "origin": "http://127.0.0.1:8000",
    "connection": "keep-alive",
    "cookie": "username-127-0-0-1-8888=\"2|1:0|10:1753687981|23:username-127-0-0-1-8888|44:OTI1NWYwNDA2MmYzNGM4ODhmZWEyMzU0NDNhZWYzZGU=|af37f3c5748e269f0b3a691141a8d9d34952092469c0df3eaa2fabbf0681f48d\"; _xsrf=2|a122885f|aa0d74d2af0b3aa32b5dde71a04b1553|1753687981",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "priority": "u=0"
  },
  "url_full": "http://127.0.0.1:8000/mcc-analysis/"
}
2025-07-29 19:31:49,996 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 19:31:49,996 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-29 19:31:50,004 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = ?
2025-07-29 19:31:50,004 - sqlalchemy.engine.Engine - INFO - SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = ?
2025-07-29 19:31:50,005 INFO sqlalchemy.engine.Engine [generated in 0.00027s] ('208378df-cfasdf32-4b91-aaea-f15f9cfeddcd',)
2025-07-29 19:31:50,005 - sqlalchemy.engine.Engine - INFO - [generated in 0.00027s] ('208378df-cfasdf32-4b91-aaea-f15f9cfeddcd',)
2025-07-29 19:31:50,018 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-29 19:31:50,018 - sqlalchemy.engine.Engine - INFO - SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-29 19:31:50,018 INFO sqlalchemy.engine.Engine [generated in 0.00024s] ('208378df-cfasdf32-4b91-aaea-f15f9cfeddcd',)
2025-07-29 19:31:50,018 - sqlalchemy.engine.Engine - INFO - [generated in 0.00024s] ('208378df-cfasdf32-4b91-aaea-f15f9cfeddcd',)
2025-07-29 19:31:50,019 - app.utils.website_url_processor - INFO - URLs already exist for scrape_request_ref_id: 208378df-cfasdf32-4b91-aaea-f15f9cfeddcd, count: 48
2025-07-29 19:31:50,021 INFO sqlalchemy.engine.Engine INSERT INTO mcc_analysis_gemini (website, scrape_request_ref_id, result_status, mcc_code, business_category, business_description, reasoning, created_at, started_at, completed_at, failed_at, last_updated, error_message, details, org_id, processing_status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-29 19:31:50,021 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_analysis_gemini (website, scrape_request_ref_id, result_status, mcc_code, business_category, business_description, reasoning, created_at, started_at, completed_at, failed_at, last_updated, error_message, details, org_id, processing_status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-29 19:31:50,021 INFO sqlalchemy.engine.Engine [generated in 0.00018s] ('https://drynotch.com', '208378df-cfasdf32-4b91-aaea-f15f9cfeddcd', None, None, None, None, None, '2025-07-29T19:31:50.020112Z', None, None, None, None, None, None, 'default', 'PENDING')
2025-07-29 19:31:50,021 - sqlalchemy.engine.Engine - INFO - [generated in 0.00018s] ('https://drynotch.com', '208378df-cfasdf32-4b91-aaea-f15f9cfeddcd', None, None, None, None, None, '2025-07-29T19:31:50.020112Z', None, None, None, None, None, None, 'default', 'PENDING')
2025-07-29 19:31:50,021 INFO sqlalchemy.engine.Engine COMMIT
2025-07-29 19:31:50,021 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-29 19:31:50,024 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 19:31:50,024 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-29 19:31:50,025 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.id = ?
2025-07-29 19:31:50,025 - sqlalchemy.engine.Engine - INFO - SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.id = ?
2025-07-29 19:31:50,025 INFO sqlalchemy.engine.Engine [generated in 0.00014s] (27,)
2025-07-29 19:31:50,025 - sqlalchemy.engine.Engine - INFO - [generated in 0.00014s] (27,)
2025-07-29 19:31:50,026 - app.routers.mcc_analysis - INFO - Created new MCC analysis with ID 27
2025-07-29 19:31:50,026 - app.routers.mcc_analysis - INFO - === QUEUEING BACKGROUND TASK for analysis 27 ===
2025-07-29 19:31:50,026 - app.routers.mcc_analysis - INFO - Background task successfully queued for analysis 27
2025-07-29 19:31:50,026 - app.routers.mcc_analysis - INFO - Task will process: website=https://drynotch.com, scrape_ref=208378df-cfasdf32-4b91-aaea-f15f9cfeddcd
2025-07-29 19:31:50,026 INFO sqlalchemy.engine.Engine ROLLBACK
2025-07-29 19:31:50,026 - sqlalchemy.engine.Engine - INFO - ROLLBACK
[2025-07-29 19:31:50][request_middleware][NO_REF] INFO: Request completed: POST /mcc-analysis/ - 200
{
  "request_id": "704d006a-63fc-408a-a350-ba14d484ee3b",
  "status_code": 200,
  "response_time_ms": 34.36
}
INFO:     127.0.0.1:40040 - "POST /mcc-analysis/ HTTP/1.1" 200 OK
2025-07-29 19:31:51,541 - app.routers.mcc_analysis - INFO - === BACKGROUND TASK STARTED for MCC analysis ID: 27 ===
2025-07-29 19:31:51,542 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 19:31:51,542 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-29 19:31:51,543 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id AS mcc_analysis_gemini_id, mcc_analysis_gemini.website AS mcc_analysis_gemini_website, mcc_analysis_gemini.scrape_request_ref_id AS mcc_analysis_gemini_scrape_request_ref_id, mcc_analysis_gemini.result_status AS mcc_analysis_gemini_result_status, mcc_analysis_gemini.mcc_code AS mcc_analysis_gemini_mcc_code, mcc_analysis_gemini.business_category AS mcc_analysis_gemini_business_category, mcc_analysis_gemini.business_description AS mcc_analysis_gemini_business_description, mcc_analysis_gemini.reasoning AS mcc_analysis_gemini_reasoning, mcc_analysis_gemini.created_at AS mcc_analysis_gemini_created_at, mcc_analysis_gemini.started_at AS mcc_analysis_gemini_started_at, mcc_analysis_gemini.completed_at AS mcc_analysis_gemini_completed_at, mcc_analysis_gemini.failed_at AS mcc_analysis_gemini_failed_at, mcc_analysis_gemini.last_updated AS mcc_analysis_gemini_last_updated, mcc_analysis_gemini.error_message AS mcc_analysis_gemini_error_message, mcc_analysis_gemini.details AS mcc_analysis_gemini_details, mcc_analysis_gemini.org_id AS mcc_analysis_gemini_org_id, mcc_analysis_gemini.processing_status AS mcc_analysis_gemini_processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.id = ?
2025-07-29 19:31:51,543 - sqlalchemy.engine.Engine - INFO - SELECT mcc_analysis_gemini.id AS mcc_analysis_gemini_id, mcc_analysis_gemini.website AS mcc_analysis_gemini_website, mcc_analysis_gemini.scrape_request_ref_id AS mcc_analysis_gemini_scrape_request_ref_id, mcc_analysis_gemini.result_status AS mcc_analysis_gemini_result_status, mcc_analysis_gemini.mcc_code AS mcc_analysis_gemini_mcc_code, mcc_analysis_gemini.business_category AS mcc_analysis_gemini_business_category, mcc_analysis_gemini.business_description AS mcc_analysis_gemini_business_description, mcc_analysis_gemini.reasoning AS mcc_analysis_gemini_reasoning, mcc_analysis_gemini.created_at AS mcc_analysis_gemini_created_at, mcc_analysis_gemini.started_at AS mcc_analysis_gemini_started_at, mcc_analysis_gemini.completed_at AS mcc_analysis_gemini_completed_at, mcc_analysis_gemini.failed_at AS mcc_analysis_gemini_failed_at, mcc_analysis_gemini.last_updated AS mcc_analysis_gemini_last_updated, mcc_analysis_gemini.error_message AS mcc_analysis_gemini_error_message, mcc_analysis_gemini.details AS mcc_analysis_gemini_details, mcc_analysis_gemini.org_id AS mcc_analysis_gemini_org_id, mcc_analysis_gemini.processing_status AS mcc_analysis_gemini_processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.id = ?
2025-07-29 19:31:51,543 INFO sqlalchemy.engine.Engine [generated in 0.00025s] (27,)
2025-07-29 19:31:51,543 - sqlalchemy.engine.Engine - INFO - [generated in 0.00025s] (27,)
2025-07-29 19:31:51,544 INFO sqlalchemy.engine.Engine UPDATE mcc_analysis_gemini SET started_at=?, processing_status=? WHERE mcc_analysis_gemini.id = ?
2025-07-29 19:31:51,544 - sqlalchemy.engine.Engine - INFO - UPDATE mcc_analysis_gemini SET started_at=?, processing_status=? WHERE mcc_analysis_gemini.id = ?
2025-07-29 19:31:51,544 INFO sqlalchemy.engine.Engine [generated in 0.00014s] ('2025-07-29T19:31:51.543892Z', 'PROCESSING', 27)
2025-07-29 19:31:51,544 - sqlalchemy.engine.Engine - INFO - [generated in 0.00014s] ('2025-07-29T19:31:51.543892Z', 'PROCESSING', 27)
2025-07-29 19:31:51,545 INFO sqlalchemy.engine.Engine COMMIT
2025-07-29 19:31:51,545 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-29 19:31:51,547 - app.routers.mcc_analysis - INFO - Updated MccAnalysis 27 status to PROCESSING
2025-07-29 19:31:51,547 - app.routers.mcc_analysis - INFO - Starting MCC classification service for analysis 27
[2025-07-29 19:31:51][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Starting MCC analysis process
{
  "scrape_request_ref_id": "208378df-cfasdf32-4b91-aaea-f15f9cfeddcd",
  "org_id": "default"
}
[2025-07-29 19:31:51][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Step 1: Getting URLs data
[2025-07-29 19:31:51][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Checking if URLs exist in database
{
  "scrape_request_ref_id": "208378df-cfasdf32-4b91-aaea-f15f9cfeddcd"
}
2025-07-29 19:31:51,547 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 19:31:51,547 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-29 19:31:51,547 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-29 19:31:51,547 - sqlalchemy.engine.Engine - INFO - SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-29 19:31:51,548 INFO sqlalchemy.engine.Engine [cached since 1.53s ago] ('208378df-cfasdf32-4b91-aaea-f15f9cfeddcd',)
2025-07-29 19:31:51,548 - sqlalchemy.engine.Engine - INFO - [cached since 1.53s ago] ('208378df-cfasdf32-4b91-aaea-f15f9cfeddcd',)
2025-07-29 19:31:51,548 - app.utils.website_url_processor - INFO - Retrieved 48 URLs for scrape_request_ref_id: 208378df-cfasdf32-4b91-aaea-f15f9cfeddcd
2025-07-29 19:31:51,549 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = ?
2025-07-29 19:31:51,549 - sqlalchemy.engine.Engine - INFO - SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = ?
2025-07-29 19:31:51,549 INFO sqlalchemy.engine.Engine [cached since 1.545s ago] ('208378df-cfasdf32-4b91-aaea-f15f9cfeddcd',)
2025-07-29 19:31:51,549 - sqlalchemy.engine.Engine - INFO - [cached since 1.545s ago] ('208378df-cfasdf32-4b91-aaea-f15f9cfeddcd',)
[2025-07-29 19:31:51][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: URLs found in database
{
  "website": "https://drynotch.com",
  "total_depths": 1,
  "total_urls": 48
}
[2025-07-29 19:31:51][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Successfully unpacked URL check result
{
  "urls_exist": true,
  "urls_data_type": "<class 'dict'>"
}
[2025-07-29 19:31:51][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Total parsed URLs found: 48
[2025-07-29 19:31:51][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Step 2: Preparing data for analysis
[2025-07-29 19:31:51][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Preparing data for MCC analysis
{
  "website": "https://drynotch.com"
}
[2025-07-29 19:31:51][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Got URLs by depth
{
  "depth_1_count": 48,
  "depth_2_count": 0,
  "scrape_request_ref_id": "208378df-cfasdf32-4b91-aaea-f15f9cfeddcd"
}
[2025-07-29 19:31:51][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Checking for existing classification results in database
2025-07-29 19:31:51,549 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-29 19:31:51,549 - sqlalchemy.engine.Engine - INFO - SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-29 19:31:51,549 INFO sqlalchemy.engine.Engine [cached since 1.531s ago] ('208378df-cfasdf32-4b91-aaea-f15f9cfeddcd',)
2025-07-29 19:31:51,549 - sqlalchemy.engine.Engine - INFO - [cached since 1.531s ago] ('208378df-cfasdf32-4b91-aaea-f15f9cfeddcd',)
2025-07-29 19:31:57,806 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
[2025-07-29 19:31:51][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Found 48 stored URL records
[2025-07-29 19:31:51][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Stored classification analysis
{
  "total_stored_urls": 48,
  "unreachable_count": 48,
  "classified_count": 0,
  "stored_categories": {
    "urls_not_reachable": 48
  }
}
[2025-07-29 19:31:51][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] WARNING: [RACE CONDITION FIX] All stored URLs marked unreachable - forcing fresh classification
{
  "total_urls": 48,
  "unreachable_urls": 48,
  "classified_urls": 0,
  "action": "FORCING_FRESH_CLASSIFICATION",
  "scrape_ref": "208378df-cfasdf32-4b91-aaea-f15f9cfeddcd",
  "fix_type": "RACE_CONDITION_PROTECTION",
  "time": "2025-07-29T19:31:51.550450"
}
[2025-07-29 19:31:51][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: No usable stored classification results found, proceeding with fresh API calls
[2025-07-29 19:31:51][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Starting soft classification of URLs
[2025-07-29 19:31:51][url_classification_208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Starting soft classification
{
  "website": "https://drynotch.com",
  "urls_depth_1_count": 48,
  "urls_depth_2_count": 0
}
[2025-07-29 19:31:51][url_classification_208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Dictionary for soft classification prepared
{
  "url_count": 48,
  "sample_urls": [
    "https://drynotch.com",
    "http://drynotch.com/cdn/shop/files/Untitled-2_dbc3a50a-6538-408a-b16d-aec289edf49d.png?v=1714240398",
    "https://drynotch.com/products/notchflex-high-waist-leggings-blue-crush"
  ]
}
[2025-07-29 19:31:51][test-analysis][NO_REF] INFO: Starting URL processing for model policy result
{
  "website": "https://drynotch.com",
  "total_urls": 48
}
[2025-07-29 19:31:51][test-analysis][NO_REF] INFO: Token calculation: system=265, base_user=988, available_for_urls=87747
[2025-07-29 19:31:51][test-analysis][NO_REF] INFO: Total URL tokens: 817, Available: 87747
[2025-07-29 19:31:51][test-analysis][NO_REF] INFO: All URLs fit within token limit
[2025-07-29 19:31:51][test-analysis][NO_REF] INFO: Final URLs after token limiting
{
  "original_url_count": 48,
  "final_url_count": 48,
  "urls_trimmed": 0,
  "system_tokens": 265,
  "base_user_tokens": 988,
  "url_tokens": 817,
  "final_total_tokens": 2070,
  "token_limit": 90000,
  "remaining_tokens": 87930
}
[2025-07-29 19:31:51][test-analysis][NO_REF] INFO: Final prompt verification
{
  "actual_prompt_tokens": 2308,
  "token_limit": 90000,
  "within_limit": true,
  "processing_time": 0.14402508735656738
}
[2025-07-29 19:31:51][url_classification_208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Sending request to OpenAI API using model gpt-4o...
[2025-07-29 19:31:51][url_classification_208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Processing URLs for classification. This may take some time...
[2025-07-29 19:31:57][url_classification_208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Usage: CompletionUsage(completion_tokens=134, prompt_tokens=2318, total_tokens=2452, completion_tokens_details=CompletionTokensDetails(accepted_prediction_tokens=0, audio_tokens=0, reasoning_tokens=0, rejected_prediction_tokens=0), prompt_tokens_details=PromptTokensDetails(audio_tokens=0, cached_tokens=0))
[2025-07-29 19:31:58][url_classification_208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Soft classification response received
{
  "response_length": 444,
  "response_preview": "```json\n{\n    \"home_page\": [0],\n    \"about_us\": [3],\n    \"terms_and_condition\": [25],\n    \"returns_cancellation_exchange\": [45],\n    \"privacy_policy\": [7],\n    \"shipping_delivery\": [47],\n    \"contact_..."
}
[2025-07-29 19:31:58][url_classification_208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Preparing data for model policy result
{
  "website": "https://drynotch.com",
  "dictionary_1_count": 48,
  "total_chars": 2784,
  "urls_sample": [
    "https://drynotch.com",
    "http://drynotch.com/cdn/shop/files/Untitled-2_dbc3a50a-6538-408a-b16d-aec289edf49d.png?v=1714240398",
    "https://drynotch.com/products/notchflex-high-waist-leggings-blue-crush"
  ]
}
[2025-07-29 19:31:58][url_classification_208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Got policy URLs classification
{
  "policy_urls": {
    "home_page": [
      0
    ],
    "about_us": [
      3
    ],
    "terms_and_condition": [
      25
    ],
    "returns_cancellation_exchange": [
      45
    ],
    "privacy_policy": [
      7
    ],
    "shipping_delivery": [
      47
    ],
    "contact_us": [
      10
    ],
    "products": [
      2,
      11,
      12,
      16,
      27
    ],
    "services": [],
    "catalogue": [
      8
    ],
    "instagram_page": [],
    "facebook_page": [],
    "twitter_page": [],
    "linkedin_page": [],
    "youtube_page": [],
    "pinterest_page": []
  },
  "categories_found": [
    "home_page",
    "about_us",
    "terms_and_condition",
    "returns_cancellation_exchange",
    "privacy_policy",
    "shipping_delivery",
    "contact_us",
    "products",
    "services",
    "catalogue",
    "instagram_page",
    "facebook_page",
    "twitter_page",
    "linkedin_page",
    "youtube_page",
    "pinterest_page"
  ]
}
[2025-07-29 19:31:58][url_classification_208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Completed soft classification
{
  "mcc_dict": {
    "home_page": [
      "https://drynotch.com"
    ],
    "about_us": [
      "https://drynotch.com/pages/about-us"
    ],
    "terms_and_condition": [
      "https://drynotch.com/policies/terms-of-service"
    ],
    "returns_cancellation_exchange": [
      "https://drynotch.com/policies/refund-policy"
    ],
    "privacy_policy": [
      "https://drynotch.com/policies/privacy-policy"
    ],
    "shipping_delivery": [
      "https://drynotch.com/pages/shipping"
    ],
    "contact_us": [
      "https://drynotch.com/pages/contact"
    ],
    "products": [
      "https://drynotch.com/products/notchflex-high-waist-leggings-blue-crush",
      "https://drynotch.com/products/blue-crush-co-ord-set#judgeme_product_reviews",
      "https://drynotch.com/products/work-it-out-straight-fit-pants",
      "https://drynotch.com/products/fiery-trio-co-ord-set",
      "https://drynotch.com/products/sleek-monochrome-set"
    ],
    "services": [],
    "catalogue": [
      "https://drynotch.com/collections"
    ],
    "instagram_page": [],
    "facebook_page": [],
    "twitter_page": [],
    "linkedin_page": [],
    "youtube_page": [],
    "pinterest_page": []
  },
  "total_classified_urls": 13,
  "priority_urls_count": 8
}
[2025-07-29 19:31:58][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Successfully unpacked soft classification result
{
  "output_df_type": "<class 'pandas.core.frame.DataFrame'>",
  "soft_classified_urls_type": "<class 'dict'>"
}
[2025-07-29 19:31:58][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Soft classification unreachable URL analysis
{
  "total_urls": 48,
  "unreachable_count": 0,
  "unreachable_ratio": "0/48",
  "unreachable_percentage": "0.0%",
  "urls_by_category": {
    "home_page": 1,
    "about_us": 1,
    "terms_and_condition": 1,
    "returns_cancellation_exchange": 1,
    "privacy_policy": 1,
    "shipping_delivery": 1,
    "contact_us": 1,
    "products": 5,
    "services": 0,
    "catalogue": 1,
    "instagram_page": 0,
    "facebook_page": 0,
    "twitter_page": 0,
    "linkedin_page": 0,
    "youtube_page": 0,
    "pinterest_page": 0
  }
}
[2025-07-29 19:31:58][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Soft classification completed
{
  "categories_found": [
    "home_page",
    "about_us",
    "terms_and_condition",
    "returns_cancellation_exchange",
    "privacy_policy",
    "shipping_delivery",
    "contact_us",
    "products",
    "services",
    "catalogue",
    "instagram_page",
    "facebook_page",
    "twitter_page",
    "linkedin_page",
    "youtube_page",
    "pinterest_page"
  ]
}
[2025-07-29 19:31:58][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Starting hard classification of URLs
[2025-07-29 19:31:58][url_classification_208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Starting hard classification verification for POLICY URLs only
{
  "website": "https://drynotch.com"
}2025-07-29 19:32:00,854 - google_genai.models - INFO - AFC is enabled with max remote calls: 20000.
2025-07-29 19:32:20,528 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent "HTTP/1.1 200 OK"
2025-07-29 19:32:20,535 - google_genai.models - INFO - AFC remote call 1 is done.

[2025-07-29 19:31:58][url_classification_208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Including policy category 'home_page' in hard classification
{
  "url_count": 1
}
[2025-07-29 19:31:58][url_classification_208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Including policy category 'about_us' in hard classification
{
  "url_count": 1
}
[2025-07-29 19:31:58][url_classification_208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Including policy category 'terms_and_condition' in hard classification
{
  "url_count": 1
}
[2025-07-29 19:31:58][url_classification_208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Including policy category 'returns_cancellation_exchange' in hard classification
{
  "url_count": 1
}
[2025-07-29 19:31:58][url_classification_208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Including policy category 'privacy_policy' in hard classification
{
  "url_count": 1
}
[2025-07-29 19:31:58][url_classification_208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Including policy category 'shipping_delivery' in hard classification
{
  "url_count": 1
}
[2025-07-29 19:31:58][url_classification_208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Including policy category 'contact_us' in hard classification
{
  "url_count": 1
}
[2025-07-29 19:31:58][url_classification_208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Including other category 'products' in hard classification
{
  "url_count": 5
}
[2025-07-29 19:31:58][url_classification_208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Including other category 'services' in hard classification
{
  "url_count": 0
}
[2025-07-29 19:31:58][url_classification_208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Including policy category 'catalogue' in hard classification
{
  "url_count": 1
}
[2025-07-29 19:31:58][url_classification_208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Skipping social media category 'instagram_page' from hard classification
{
  "url_count": 0,
  "reason": "social_media_uses_soft_classification_only"
}
[2025-07-29 19:31:58][url_classification_208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Skipping social media category 'facebook_page' from hard classification
{
  "url_count": 0,
  "reason": "social_media_uses_soft_classification_only"
}
[2025-07-29 19:31:58][url_classification_208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Skipping social media category 'twitter_page' from hard classification
{
  "url_count": 0,
  "reason": "social_media_uses_soft_classification_only"
}
[2025-07-29 19:31:58][url_classification_208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Skipping social media category 'linkedin_page' from hard classification
{
  "url_count": 0,
  "reason": "social_media_uses_soft_classification_only"
}
[2025-07-29 19:31:58][url_classification_208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Skipping social media category 'youtube_page' from hard classification
{
  "url_count": 0,
  "reason": "social_media_uses_soft_classification_only"
}
[2025-07-29 19:31:58][url_classification_208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Skipping social media category 'pinterest_page' from hard classification
{
  "url_count": 0,
  "reason": "social_media_uses_soft_classification_only"
}
[2025-07-29 19:31:58][test-analysis][NO_REF] INFO: Hard classification input
{
  "total_urls": 13,
  "website": "https://drynotch.com",
  "url_limit": 20
}
[2025-07-29 19:31:58][test-analysis][NO_REF] INFO: All URLs fit within hard classification limit
{
  "total_urls": 13,
  "limit": 20
}
[2025-07-29 19:31:58][gemini_optimizer_post_soft_classification][NO_REF] INFO: Optimizing Gemini call for task_type: post_soft_classification
{
  "received_task_type": "post_soft_classification"
}
[2025-07-29 19:32:00][post_soft_classification_unknown][NO_REF] INFO: Starting Gemini API call
{
  "model": "gemini-2.5-flash",
  "timeout": 120,
  "max_retries": 3,
  "prompt_length": 5870,
  "context": {
    "task_type": "post_soft_classification"
  }
}
[2025-07-29 19:32:00][post_soft_classification_unknown][NO_REF] INFO: Gemini API attempt 1/3
[2025-07-29 19:32:20][post_soft_classification_unknown][NO_REF] INFO: Gemini API Usage: cache_tokens_details=None cached_content_token_count=None candidates_token_count=466 candidates_tokens_details=None prompt_token_count=1457 prompt_tokens_details=[ModalityTokenCount(
  modality=<MediaModality.TEXT: 'TEXT'>,
  token_count=1457
)] thoughts_token_count=1962 tool_use_prompt_token_count=None tool_use_prompt_tokens_details=None total_token_count=3885 traffic_type=None
[2025-07-29 19:32:20][post_soft_classification_unknown][NO_REF] INFO: Gemini API call successful
{
  "attempt": 1,
  "response_length": 352,
  "finish_reason": "STOP"
}
[2025-07-29 19:32:21][url_classification_208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Gemini response received for hard classification
{
  "response_length": 352,
  "response_preview": "```json\n{\n    \"home_page\": [9],\n    \"about_us\": [3],\n    \"terms_and_condition\": [],\n    \"returns_cancellation_exchange\": [1, 5, 8, 10, 11],\n    \"privacy_policy\": [],\n    \"shipping_delivery\": [1, 5, 7,..."
}
[2025-07-29 19:32:21][url_classification_208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Parsing hard classification response
{
  "response": "```json\n{\n    \"home_page\": [9],\n    \"about_us\": [3],\n    \"terms_and_condition\": [],\n    \"returns_cancellation_exchange\": [1, 5, 8, 10, 11],\n    \"privacy_policy\": [],\n    \"shipping_delivery\": [1, 5, 7, 8, 10, 11],\n    \"contact_us\": [4],\n    \"catalogue\": [1, 5, 6, 8, 9, 10, 11],\n    \"urls_not_reachable\": [],\n    \"Unreachable_via_tool\": [0, 2, 12]\n}\n```"
}
[2025-07-29 19:32:21][url_classification_208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Verified indices dictionary created
{
  "indices_categories": [
    "home_page",
    "about_us",
    "terms_and_condition",
    "returns_cancellation_exchange",
    "privacy_policy",
    "shipping_delivery",
    "contact_us",
    "catalogue",
    "urls_not_reachable",
    "Unreachable_via_tool"
  ],
  "total_indices": 24
}
[2025-07-29 19:32:21][url_classification_208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Hard classification reachability results
{
  "unreachable_urls_count": 3,
  "reachable_urls_count": 21,
  "unreachable_urls": [
    0,
    2,
    12
  ],
  "website": "https://drynotch.com",
  "backup_flow_trigger": true
}
[2025-07-29 19:32:21][url_classification_208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Verified indices dictionary created
{
  "indices_categories": [
    "home_page",
    "about_us",
    "terms_and_condition",
    "returns_cancellation_exchange",
    "privacy_policy",
    "shipping_delivery",
    "contact_us",
    "catalogue",
    "urls_not_reachable",
    "Unreachable_via_tool"
  ],
  "total_indices": 24
}
[2025-07-29 19:32:21][url_classification_208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Verified URLs dictionary created with social media merged
{
  "verified_categories": [
    "home_page",
    "about_us",
    "terms_and_condition",
    "returns_cancellation_exchange",
    "privacy_policy",
    "shipping_delivery",
    "contact_us",
    "catalogue",
    "urls_not_reachable",
    "Unreachable_via_tool"
  ],
  "category_counts": {
    "home_page": 1,
    "about_us": 1,
    "terms_and_condition": 0,
    "returns_cancellation_exchange": 5,
    "privacy_policy": 0,
    "shipping_delivery": 6,
    "contact_us": 1,
    "catalogue": 7,
    "urls_not_reachable": 0,
    "Unreachable_via_tool": 3
  },
  "total_verified_urls": 24,
  "social_media_merged": true
}
[2025-07-29 19:32:21][url_classification_208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Completed hard classification verification with social media merge
{
  "verified_categories": [
    "home_page",
    "about_us",
    "terms_and_condition",
    "returns_cancellation_exchange",
    "privacy_policy",
    "shipping_delivery",
    "contact_us",
    "catalogue",
    "urls_not_reachable",
    "Unreachable_via_tool"
  ],
  "total_verified_urls": 24,
  "policy_hard_classified": true,
  "social_media_soft_classified": true
}
[2025-07-29 19:32:21][url_classification_208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Saving hard classification results to database
2025-07-29 19:32:21,538 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 19:32:21,538 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-29 19:32:21,538 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-29 19:32:21,538 - sqlalchemy.engine.Engine - INFO - SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-29 19:32:21,538 INFO sqlalchemy.engine.Engine [cached since 31.52s ago] ('208378df-cfasdf32-4b91-aaea-f15f9cfeddcd',)
2025-07-29 19:32:21,538 - sqlalchemy.engine.Engine - INFO - [cached since 31.52s ago] ('208378df-cfasdf32-4b91-aaea-f15f9cfeddcd',)
[2025-07-29 19:32:21][url_classification_208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Retrieved 48 URL records for hard classification update
2025-07-29 19:32:21,541 INFO sqlalchemy.engine.Engine UPDATE website_urls_gemini SET hard_class=? WHERE website_urls_gemini.id = ?
2025-07-29 19:32:21,541 - sqlalchemy.engine.Engine - INFO - UPDATE website_urls_gemini SET hard_class=? WHERE website_urls_gemini.id = ?
2025-07-29 19:32:21,541 INFO sqlalchemy.engine.Engine [generated in 0.00028s] [('["home_page", "catalogue"]', 2210), ('["returns_cancellation_exchange", "shipping_delivery", "catalogue"]', 2212), ('["about_us"]', 2213), ('["Unreachable_via_tool"]', 2217), ('["catalogue"]', 2218), ('["contact_us"]', 2220), ('["returns_cancellation_exchange", "shipping_delivery", "catalogue"]', 2221), ('["returns_cancellation_exchange", "shipping_delivery", "catalogue"]', 2222)  ... displaying 10 of 13 total bound parameter sets ...  ('["Unreachable_via_tool"]', 2255), ('["shipping_delivery"]', 2257)]
2025-07-29 19:32:21,541 - sqlalchemy.engine.Engine - INFO - [generated in 0.00028s] [('["home_page", "catalogue"]', 2210), ('["returns_cancellation_exchange", "shipping_delivery", "catalogue"]', 2212), ('["about_us"]', 2213), ('["Unreachable_via_tool"]', 2217), ('["catalogue"]', 2218), ('["contact_us"]', 2220), ('["returns_cancellation_exchange", "shipping_delivery", "catalogue"]', 2221), ('["returns_cancellation_exchange", "shipping_delivery", "catalogue"]', 2222)  ... displaying 10 of 13 total bound parameter sets ...  ('["Unreachable_via_tool"]', 2255), ('["shipping_delivery"]', 2257)]
2025-07-29 19:32:21,553 INFO sqlalchemy.engine.Engine COMMIT
2025-07-29 19:32:21,553 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-07-29 19:32:21][url_classification_208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Updated 24 database records with hard classification
[2025-07-29 19:32:21][url_classification_208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Hard classification summary: 24 total URLs across 10 categories
[2025-07-29 19:32:21][url_classification_208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Hard classification category breakdown: {'home_page': 1, 'about_us': 1, 'terms_and_condition': 0, 'returns_cancellation_exchange': 5, 'privacy_policy': 0, 'shipping_delivery': 6, 'contact_us': 1, 'catalogue': 7, 'urls_not_reachable': 0, 'Unreachable_via_tool': 3}
[2025-07-29 19:32:21][url_classification_208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Successfully saved hard classification results to database
[2025-07-29 19:32:21][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Hard classification completed
{
  "categories_found": [
    "home_page",
    "about_us",
    "terms_and_condition",
    "returns_cancellation_exchange",
    "privacy_policy",
    "shipping_delivery",
    "contact_us",
    "catalogue",
    "urls_not_reachable",
    "Unreachable_via_tool"
  ]
}
[2025-07-29 19:32:21][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Hard classification results - detailed breakdown
{
  "category_counts": {
    "home_page": 1,
    "about_us": 1,
    "terms_and_condition": 0,
    "returns_cancellation_exchange": 5,
    "privacy_policy": 0,
    "shipping_delivery": 6,
    "contact_us": 1,
    "catalogue": 7,
    "urls_not_reachable": 0,
    "Unreachable_via_tool": 3
  },
  "total_reachable_urls": 24,
  "priority_reachable_urls": 9,
  "total_unreachable_urls": 0,
  "unreachable_percentage": "0.0%"
}
[2025-07-29 19:32:21][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Some URLs are reachable - continuing with normal flow
{
  "total_reachable_urls": 24,
  "priority_urls_found": 9,
  "decision": "NORMAL_FLOW_CONTINUES",
  "reachable_categories": [
    "home_page",
    "about_us",
    "returns_cancellation_exchange",
    "shipping_delivery",
    "contact_us",
    "catalogue"
  ]
}
[2025-07-29 19:32:21][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Starting priority URL filtering
{
  "input_categories": [
    "home_page",
    "about_us",
    "terms_and_condition",
    "returns_cancellation_exchange",
    "privacy_policy",
    "shipping_delivery",
    "contact_us",
    "catalogue",
    "urls_not_reachable",
    "Unreachable_via_tool"
  ],
  "priority_categories_defined": [
    "about_us",
    "catalogue",
    "products",
    "home_page"
  ]
}
[2025-07-29 19:32:21][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Filtering priority URLs for MCC analysis - input analysis
{
  "total_categories": 10,
  "input_category_counts": {
    "home_page": 1,
    "about_us": 1,
    "terms_and_condition": 0,
    "returns_cancellation_exchange": 5,
    "privacy_policy": 0,
    "shipping_delivery": 6,
    "contact_us": 1,
    "catalogue": 7,
    "urls_not_reachable": 0,
    "Unreachable_via_tool": 3
  },
  "priority_categories": [
    "about_us",
    "catalogue",
    "products",
    "home_page"
  ],
  "max_urls_for_mcc": 18
}
[2025-07-29 19:32:21][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: URLs per category calculated: 18
[2025-07-29 19:32:21][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] DEBUG: Processing category 'home_page': 1 URLs
{
  "category": "home_page",
  "url_count": 1,
  "is_priority": true,
  "has_urls": true
}
[2025-07-29 19:32:21][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: ✅ Added 1 URLs for priority category 'home_page'
{
  "category": "home_page",
  "added_urls": 1,
  "total_available": 1,
  "urls_per_category_limit": 18
}
[2025-07-29 19:32:21][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] DEBUG: Processing category 'about_us': 1 URLs
{
  "category": "about_us",
  "url_count": 1,
  "is_priority": true,
  "has_urls": true
}
[2025-07-29 19:32:21][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: ✅ Added 1 URLs for priority category 'about_us'
{
  "category": "about_us",
  "added_urls": 1,
  "total_available": 1,
  "urls_per_category_limit": 18
}
[2025-07-29 19:32:21][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] DEBUG: Processing category 'terms_and_condition': 0 URLs
{
  "category": "terms_and_condition",
  "url_count": 0,
  "is_priority": false,
  "has_urls": false
}
[2025-07-29 19:32:21][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] DEBUG: Processing category 'returns_cancellation_exchange': 5 URLs
{
  "category": "returns_cancellation_exchange",
  "url_count": 5,
  "is_priority": false,
  "has_urls": true
}
[2025-07-29 19:32:21][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] DEBUG: Processing category 'privacy_policy': 0 URLs
{
  "category": "privacy_policy",
  "url_count": 0,
  "is_priority": false,
  "has_urls": false
}
[2025-07-29 19:32:21][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] DEBUG: Processing category 'shipping_delivery': 6 URLs
{
  "category": "shipping_delivery",
  "url_count": 6,
  "is_priority": false,
  "has_urls": true
}
[2025-07-29 19:32:21][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] DEBUG: Processing category 'contact_us': 1 URLs
{
  "category": "contact_us",
  "url_count": 1,
  "is_priority": false,
  "has_urls": true
}
[2025-07-29 19:32:21][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] DEBUG: Processing category 'catalogue': 7 URLs
{
  "category": "catalogue",
  "url_count": 7,
  "is_priority": true,
  "has_urls": true
}
[2025-07-29 19:32:21][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: ✅ Added 7 URLs for priority category 'catalogue'
{
  "category": "catalogue",
  "added_urls": 7,
  "total_available": 7,
  "urls_per_category_limit": 18
}
[2025-07-29 19:32:21][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] DEBUG: Processing category 'urls_not_reachable': 0 URLs
{
  "category": "urls_not_reachable",
  "url_count": 0,
  "is_priority": false,
  "has_urls": false
}
[2025-07-29 19:32:21][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] DEBUG: Processing category 'Unreachable_via_tool': 3 URLs
{
  "category": "Unreachable_via_tool",
  "url_count": 3,
  "is_priority": false,
  "has_urls": true
}
[2025-07-29 19:32:21][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Priority URLs filtered
{
  "priority_categories": [
    "home_page",
    "about_us",
    "catalogue"
  ],
  "total_priority_urls": 9,
  "fallback_applied": false
}
[2025-07-29 19:32:21][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Priority URL filtering completed
{
  "priority_url_counts": {
    "home_page": 1,
    "about_us": 1,
    "catalogue": 7
  },
  "total_priority_urls": 9
}
[2025-07-29 19:32:21][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Data preparation completed
{
  "total_classified_urls": 24,
  "priority_urls_count": 9
}
[2025-07-29 19:32:21][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Successfully unpacked data preparation result
{
  "classified_urls_type": "<class 'dict'>",
  "priority_urls_type": "<class 'dict'>",
  "classified_urls_is_none": false,
  "priority_urls_is_none": false
}
[2025-07-29 19:32:21][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Step 2 completed in 30.01 seconds
[2025-07-29 19:32:21][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Step 3: Starting MCC classification
{
  "website": "https://drynotch.com",
  "priority_url_categories": [
    "home_page",
    "about_us",
    "catalogue"
  ],
  "total_priority_urls": 9
}
[2025-07-29 19:32:21][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: Starting MCC classification
{
  "website": "https://drynotch.com",
  "priority_categories": [
    "home_page",
    "about_us",
    "catalogue"
  ]
}
[2025-07-29 19:32:21][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: 🔍 Extracting text content from priority URLs for analysis
[2025-07-29 19:32:21][208378df-cfasdf32-4b91-aaea-f15f9cfeddcd][NO_REF] INFO: 📄 Extracting text from home_page: https://drynotch.com
[2025-07-29 19:32:21][text_extraction][NO_REF] INFO: Starting text extraction
{
  "url": "https://drynotch.com",
  "timeout": 60
}
