nohup: ignoring input
2025-07-28 00:49:39,509 - app.main - INFO - Request logging middleware initialized and ready to capture requests
INFO:     Started server process [58640]
INFO:     Waiting for application startup.
2025-07-28 00:49:39,513 - app.main - INFO - Initializing application
2025-07-28 00:49:39,514 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-28 00:49:39,514 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 00:49:39,514 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("mcc_analysis_gemini")
2025-07-28 00:49:39,514 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("mcc_analysis_gemini")
2025-07-28 00:49:39,514 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-28 00:49:39,514 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-28 00:49:39,514 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("website_urls_gemini")
2025-07-28 00:49:39,514 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("website_urls_gemini")
2025-07-28 00:49:39,514 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-28 00:49:39,514 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-28 00:49:39,514 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("general_logs_gemini")
2025-07-28 00:49:39,514 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("general_logs_gemini")
2025-07-28 00:49:39,514 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-28 00:49:39,514 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-28 00:49:39,514 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("gemini_api_log_gemini")
2025-07-28 00:49:39,514 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("gemini_api_log_gemini")
2025-07-28 00:49:39,514 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-28 00:49:39,514 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-28 00:49:39,515 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("scrape_request_tracker_gemini")
2025-07-28 00:49:39,515 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("scrape_request_tracker_gemini")
2025-07-28 00:49:39,515 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-28 00:49:39,515 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-28 00:49:39,515 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("scraped_urls_gemini")
2025-07-28 00:49:39,515 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("scraped_urls_gemini")
2025-07-28 00:49:39,515 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-28 00:49:39,515 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-28 00:49:39,515 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("websites_gemini")
2025-07-28 00:49:39,515 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("websites_gemini")
2025-07-28 00:49:39,515 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-28 00:49:39,515 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-28 00:49:39,515 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("mcc_url_classification_gemini")
2025-07-28 00:49:39,515 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("mcc_url_classification_gemini")
2025-07-28 00:49:39,515 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-28 00:49:39,515 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-28 00:49:39,515 INFO sqlalchemy.engine.Engine COMMIT
2025-07-28 00:49:39,515 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-28 00:49:39,515 - app.main - INFO - Database initialized successfully
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:9000 (Press CTRL+C to quit)
[2025-07-28 00:49:49][request_middleware][NO_REF] INFO: Request started: GET /docs
{
  "request_id": "1d951d03-39fa-4440-87d9-cc5b46755947",
  "client_ip": "127.0.0.1",
  "user_agent": "Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0",
  "query_params": null,
  "headers": {
    "host": "127.0.0.1:9000",
    "user-agent": "Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0",
    "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "accept-language": "en-US,en;q=0.5",
    "accept-encoding": "gzip, deflate, br, zstd",
    "connection": "keep-alive",
    "upgrade-insecure-requests": "1",
    "sec-fetch-dest": "document",
    "sec-fetch-mode": "navigate",
    "sec-fetch-site": "none",
    "sec-fetch-user": "?1",
    "priority": "u=0, i"
  },
  "url_full": "http://127.0.0.1:9000/docs"
}
[2025-07-28 00:49:49][request_middleware][NO_REF] INFO: Request completed: GET /docs - 200
{
  "request_id": "1d951d03-39fa-4440-87d9-cc5b46755947",
  "status_code": 200,
  "response_time_ms": 0.36
}
INFO:     127.0.0.1:34480 - "GET /docs HTTP/1.1" 200 OK
[2025-07-28 00:49:49][request_middleware][NO_REF] INFO: Request started: GET /openapi.json
{
  "request_id": "9e05549b-1d73-4451-acb5-234872ec9731",
  "client_ip": "127.0.0.1",
  "user_agent": "Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0",
  "query_params": null,
  "headers": {
    "host": "127.0.0.1:9000",
    "user-agent": "Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0",
    "accept": "application/json,*/*",
    "accept-language": "en-US,en;q=0.5",
    "accept-encoding": "gzip, deflate, br, zstd",
    "referer": "http://127.0.0.1:9000/docs",
    "connection": "keep-alive",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "priority": "u=4"
  },
  "url_full": "http://127.0.0.1:9000/openapi.json"
}
[2025-07-28 00:49:49][request_middleware][NO_REF] INFO: Request completed: GET /openapi.json - 200
{
  "request_id": "9e05549b-1d73-4451-acb5-234872ec9731",
  "status_code": 200,
  "response_time_ms": 6.4
}
INFO:     127.0.0.1:34480 - "GET /openapi.json HTTP/1.1" 200 OK
2025-07-28 00:49:55,630 - app.routers.mcc_analysis - INFO - Processing MCC analysis request for https://www.epaal.in/ with ref_id ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42
[2025-07-28 00:49:55][request_middleware][NO_REF] INFO: Request started: POST /mcc-analysis/
{
  "request_id": "41c2a7bf-2db2-40ef-a9ca-5d61431250f1",
  "client_ip": "127.0.0.1",
  "user_agent": "Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0",
  "query_params": null,
  "headers": {
    "host": "127.0.0.1:9000",
    "user-agent": "Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0",
    "accept": "application/json",
    "accept-language": "en-US,en;q=0.5",
    "accept-encoding": "gzip, deflate, br, zstd",
    "referer": "http://127.0.0.1:9000/docs",
    "content-type": "application/json",
    "content-length": "6965",
    "origin": "http://127.0.0.1:9000",
    "connection": "keep-alive",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "priority": "u=0"
  },
  "url_full": "http://127.0.0.1:9000/mcc-analysis/"
}
2025-07-28 00:49:55,631 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-28 00:49:55,631 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 00:49:55,639 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = ?
2025-07-28 00:49:55,639 - sqlalchemy.engine.Engine - INFO - SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = ?
2025-07-28 00:49:55,639 INFO sqlalchemy.engine.Engine [generated in 0.00042s] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42',)
2025-07-28 00:49:55,639 - sqlalchemy.engine.Engine - INFO - [generated in 0.00042s] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42',)
2025-07-28 00:49:55,642 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-28 00:49:55,642 - sqlalchemy.engine.Engine - INFO - SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-28 00:49:55,642 INFO sqlalchemy.engine.Engine [generated in 0.00020s] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42',)
2025-07-28 00:49:55,642 - sqlalchemy.engine.Engine - INFO - [generated in 0.00020s] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42',)
2025-07-28 00:49:55,651 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,651 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,651 INFO sqlalchemy.engine.Engine [generated in 0.00041s (insertmanyvalues) 1/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,651 - sqlalchemy.engine.Engine - INFO - [generated in 0.00041s (insertmanyvalues) 1/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,651 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,651 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,651 INFO sqlalchemy.engine.Engine [insertmanyvalues 2/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/earbud-case', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,651 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 2/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/earbud-case', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,651 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,651 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,651 INFO sqlalchemy.engine.Engine [insertmanyvalues 3/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/epaal-protective-case-for-xiaomi-power-bank-4i-20000mah-33w-power-bank', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,651 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 3/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/epaal-protective-case-for-xiaomi-power-bank-4i-20000mah-33w-power-bank', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,651 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,651 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,651 INFO sqlalchemy.engine.Engine [insertmanyvalues 4/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/pages/about-us', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,651 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 4/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/pages/about-us', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,651 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,651 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,651 INFO sqlalchemy.engine.Engine [insertmanyvalues 5/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/epaal-rugged-and-tough-bracelet-silicone-replacement-strap-for-xiaomi-mi-band-5#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,651 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 5/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/epaal-rugged-and-tough-bracelet-silicone-replacement-strap-for-xiaomi-mi-band-5#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,651 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,651 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,651 INFO sqlalchemy.engine.Engine [insertmanyvalues 6/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/epaal-tpu-silicon-band-strap-for-huawei-honor-band-5-honor-band-4', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,651 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 6/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/epaal-tpu-silicon-band-strap-for-huawei-honor-band-5-honor-band-4', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,651 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,651 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,651 INFO sqlalchemy.engine.Engine [insertmanyvalues 7/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/realme-watch-3', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,651 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 7/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/realme-watch-3', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,652 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,652 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,652 INFO sqlalchemy.engine.Engine [insertmanyvalues 8/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/realme-watch-2', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,652 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 8/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/realme-watch-2', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,652 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,652 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,652 INFO sqlalchemy.engine.Engine [insertmanyvalues 9/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/selfie-stick', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,652 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 9/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/selfie-stick', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,652 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,652 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,652 INFO sqlalchemy.engine.Engine [insertmanyvalues 10/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/#', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,652 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 10/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/#', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,652 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,652 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,652 INFO sqlalchemy.engine.Engine [insertmanyvalues 11/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/bluetooth-earphones-1', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,652 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 11/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/bluetooth-earphones-1', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,652 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,652 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,652 INFO sqlalchemy.engine.Engine [insertmanyvalues 12/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/spiral-triple-color-2-pcs', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,652 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 12/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/spiral-triple-color-2-pcs', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,652 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,652 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,652 INFO sqlalchemy.engine.Engine [insertmanyvalues 13/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/pages/shipping-policy', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,652 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 13/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/pages/shipping-policy', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,652 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,652 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,652 INFO sqlalchemy.engine.Engine [insertmanyvalues 14/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/epaal%C2%AE-magnetic-usb-dock-charger-cable-for-realme-watch-2-watch-2-pro-1-meter-long', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,652 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 14/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/epaal%C2%AE-magnetic-usb-dock-charger-cable-for-realme-watch-2-watch-2-pro-1-meter-long', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,652 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,652 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,652 INFO sqlalchemy.engine.Engine [insertmanyvalues 15/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/iwatch-38mm', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,652 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 15/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/iwatch-38mm', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,652 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,652 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,652 INFO sqlalchemy.engine.Engine [insertmanyvalues 16/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/cdn', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,652 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 16/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/cdn', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,652 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,652 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,652 INFO sqlalchemy.engine.Engine [insertmanyvalues 17/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/bibs', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,652 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 17/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/bibs', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,652 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,652 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,652 INFO sqlalchemy.engine.Engine [insertmanyvalues 18/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/cdn/shop/files/Logo25-Apr-2023.png?height=628&pad_color=ffffff&v=1682421745&width=1200', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,652 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 18/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/cdn/shop/files/Logo25-Apr-2023.png?height=628&pad_color=ffffff&v=1682421745&width=1200', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,652 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,652 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,653 INFO sqlalchemy.engine.Engine [insertmanyvalues 19/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/epaal%C2%AE-22mm-universal-silicone-strap-with-dots-texture-for-samsung-watch-3-45mm-samsung-gear-s3-classic-frontier-huawei-gt2-realme-s-s-pro', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,653 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 19/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/epaal%C2%AE-22mm-universal-silicone-strap-with-dots-texture-for-samsung-watch-3-45mm-samsung-gear-s3-classic-frontier-huawei-gt2-realme-s-s-pro', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,653 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,653 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,653 INFO sqlalchemy.engine.Engine [insertmanyvalues 20/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,653 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 20/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,653 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,653 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,653 INFO sqlalchemy.engine.Engine [insertmanyvalues 21/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/honor-band-6', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,653 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 21/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/honor-band-6', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,653 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,653 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,653 INFO sqlalchemy.engine.Engine [insertmanyvalues 22/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/epaal%C2%AE-regular-buckle-breathable-sports-silicone-strap-for-honor-band-6-huawei-band-6', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,653 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 22/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/epaal%C2%AE-regular-buckle-breathable-sports-silicone-strap-for-honor-band-6-huawei-band-6', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,653 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,653 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,653 INFO sqlalchemy.engine.Engine [insertmanyvalues 23/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/realme-watch', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,653 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 23/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/realme-watch', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,653 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,653 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,653 INFO sqlalchemy.engine.Engine [insertmanyvalues 24/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/new-collection', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,653 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 24/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/new-collection', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,653 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,653 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,653 INFO sqlalchemy.engine.Engine [insertmanyvalues 25/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/8-in-1-set-of-spiral-usb-and-earphone-cable-protectors-winder-sticker-clips-organizer-clip', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,653 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 25/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/8-in-1-set-of-spiral-usb-and-earphone-cable-protectors-winder-sticker-clips-organizer-clip', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,653 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,653 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,653 INFO sqlalchemy.engine.Engine [insertmanyvalues 26/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/best-selling-collection', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,653 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 26/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/best-selling-collection', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,653 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,653 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,653 INFO sqlalchemy.engine.Engine [insertmanyvalues 27/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/20-mm-strap', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,653 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 27/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/20-mm-strap', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,653 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,653 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,653 INFO sqlalchemy.engine.Engine [insertmanyvalues 28/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'http://www.epaal.in/cdn/shop/files/Logo25-Apr-2023.png?height=628&pad_color=ffffff&v=1682421745&width=1200', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,653 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 28/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'http://www.epaal.in/cdn/shop/files/Logo25-Apr-2023.png?height=628&pad_color=ffffff&v=1682421745&width=1200', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,653 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,653 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,654 INFO sqlalchemy.engine.Engine [insertmanyvalues 29/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/search', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,654 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 29/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/search', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,654 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,654 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,654 INFO sqlalchemy.engine.Engine [insertmanyvalues 30/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/pages/terms-conditions', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,654 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 30/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/pages/terms-conditions', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,654 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,654 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,654 INFO sqlalchemy.engine.Engine [insertmanyvalues 31/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/teethers', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,654 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 31/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/teethers', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,654 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,654 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,654 INFO sqlalchemy.engine.Engine [insertmanyvalues 32/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/pages/returns', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,654 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 32/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/pages/returns', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,654 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,654 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,654 INFO sqlalchemy.engine.Engine [insertmanyvalues 33/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/home-kitchen', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,654 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 33/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/home-kitchen', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,654 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,654 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,654 INFO sqlalchemy.engine.Engine [insertmanyvalues 34/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/realme-watch-accessories', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,654 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 34/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/realme-watch-accessories', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,654 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,654 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,654 INFO sqlalchemy.engine.Engine [insertmanyvalues 35/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/iwatch-42mm', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,654 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 35/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/iwatch-42mm', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,654 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,654 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,654 INFO sqlalchemy.engine.Engine [insertmanyvalues 36/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/epaal-rugged-and-tough-bracelet-silicone-replacement-strap-for-xiaomi-mi-band-5', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,654 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 36/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/epaal-rugged-and-tough-bracelet-silicone-replacement-strap-for-xiaomi-mi-band-5', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,654 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,654 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,654 INFO sqlalchemy.engine.Engine [insertmanyvalues 37/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/epaal-eplr1sb-bluetooth-selfie-stick-tripod-with-fill-lights-black', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,654 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 37/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/epaal-eplr1sb-bluetooth-selfie-stick-tripod-with-fill-lights-black', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,655 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,655 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,655 INFO sqlalchemy.engine.Engine [insertmanyvalues 38/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/#MainContent', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,655 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 38/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/#MainContent', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,655 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,655 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,655 INFO sqlalchemy.engine.Engine [insertmanyvalues 39/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/pages/contact-us', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,655 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 39/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/pages/contact-us', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,655 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,655 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,655 INFO sqlalchemy.engine.Engine [insertmanyvalues 40/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/cables-and-connectors', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,655 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 40/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/cables-and-connectors', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,655 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,655 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,655 INFO sqlalchemy.engine.Engine [insertmanyvalues 41/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/spiral-triple-color-1-pc-1-4-meters-full-size-cable-cord-charger-protector-saver-winder-for-iphone-android-charging-cables', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,655 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 41/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/spiral-triple-color-1-pc-1-4-meters-full-size-cable-cord-charger-protector-saver-winder-for-iphone-android-charging-cables', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,655 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,655 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,655 INFO sqlalchemy.engine.Engine [insertmanyvalues 42/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/epaal-19mm-universal-silicone-strap-with-dash-texture-metal-buckle-for-noise-colorfit-pulse-noise-colorfit-pro-2-oxy-boat-storm-realme-watch', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,655 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 42/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/epaal-19mm-universal-silicone-strap-with-dash-texture-metal-buckle-for-noise-colorfit-pulse-noise-colorfit-pro-2-oxy-boat-storm-realme-watch', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,655 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,655 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,655 INFO sqlalchemy.engine.Engine [insertmanyvalues 43/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/22-mm-straps', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,655 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 43/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/22-mm-straps', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,655 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,655 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,655 INFO sqlalchemy.engine.Engine [insertmanyvalues 44/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/cart', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,655 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 44/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/cart', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,655 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,655 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,656 INFO sqlalchemy.engine.Engine [insertmanyvalues 45/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/epaal%C2%AE-spiral-cable-protector-winder-sleeve-for-protecting-expensive-cables', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,656 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 45/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/epaal%C2%AE-spiral-cable-protector-winder-sleeve-for-protecting-expensive-cables', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,656 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,656 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,656 INFO sqlalchemy.engine.Engine [insertmanyvalues 46/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/epaal%C2%AE-19mm-universal-silicone-strap-with-flat-metal-buckle-for-noise-colorfit-pulse-noise-colorfit-pro-2-oxy-boat-storm-realme-watch', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,656 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 46/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/epaal%C2%AE-19mm-universal-silicone-strap-with-flat-metal-buckle-for-noise-colorfit-pulse-noise-colorfit-pro-2-oxy-boat-storm-realme-watch', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,656 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,656 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,656 INFO sqlalchemy.engine.Engine [insertmanyvalues 47/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/epaal-4-pcs-cartoon-3d-design-protective-case-for-18w-20w-iphone-14-13-12-11-pro-max-fast-charging-cable-adapter-charger-with-lightning-data-cable-case-for-iphone-charger', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,656 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 47/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/epaal-4-pcs-cartoon-3d-design-protective-case-for-18w-20w-iphone-14-13-12-11-pro-max-fast-charging-cable-adapter-charger-with-lightning-data-cable-case-for-iphone-charger', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,656 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,656 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,656 INFO sqlalchemy.engine.Engine [insertmanyvalues 48/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/epaal%C2%AE-19mm-universal-silicone-strap-with-dots-texture-for-noise-colorfit-pulse-noise-colorfit-pro-2-oxy-boat-storm-realme-watch', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,656 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 48/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/epaal%C2%AE-19mm-universal-silicone-strap-with-dots-texture-for-noise-colorfit-pulse-noise-colorfit-pro-2-oxy-boat-storm-realme-watch', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,656 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,656 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,656 INFO sqlalchemy.engine.Engine [insertmanyvalues 49/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/set-of-4-spiral-cable-protector-winder-sleeves-for-protecting-expensive-cables', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,656 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 49/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/set-of-4-spiral-cable-protector-winder-sleeves-for-protecting-expensive-cables', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,656 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,656 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,656 INFO sqlalchemy.engine.Engine [insertmanyvalues 50/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/epaal%E2%84%A2-22-mm-stainless-steel-metal-band-strap-bracelet-for-amazfit-pace-stratos-samsung-gear-s3-frontier-classic-smartwatch', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,656 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 50/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/epaal%E2%84%A2-22-mm-stainless-steel-metal-band-strap-bracelet-for-amazfit-pace-stratos-samsung-gear-s3-frontier-classic-smartwatch', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,656 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,656 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,656 INFO sqlalchemy.engine.Engine [insertmanyvalues 51/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/home-electonics', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,656 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 51/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/home-electonics', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,656 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,656 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,656 INFO sqlalchemy.engine.Engine [insertmanyvalues 52/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/baby-products', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,656 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 52/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/baby-products', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,656 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,656 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,656 INFO sqlalchemy.engine.Engine [insertmanyvalues 53/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/sports-silicone-replacement-strap-wristband-for-mi-band-5', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,656 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 53/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/sports-silicone-replacement-strap-wristband-for-mi-band-5', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,657 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,657 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,657 INFO sqlalchemy.engine.Engine [insertmanyvalues 54/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/realme-watch-s-pro', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,657 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 54/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/realme-watch-s-pro', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,657 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,657 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,657 INFO sqlalchemy.engine.Engine [insertmanyvalues 55/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,657 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 55/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,657 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,657 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,657 INFO sqlalchemy.engine.Engine [insertmanyvalues 56/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/pages/cancellation-policy', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,657 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 56/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/pages/cancellation-policy', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,657 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,657 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,657 INFO sqlalchemy.engine.Engine [insertmanyvalues 57/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/computer-mouse', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,657 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 57/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/computer-mouse', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,657 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,657 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,657 INFO sqlalchemy.engine.Engine [insertmanyvalues 58/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/apparels-clothing-1', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,657 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 58/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/apparels-clothing-1', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,657 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,657 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,657 INFO sqlalchemy.engine.Engine [insertmanyvalues 59/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/pages/privacy-policy', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,657 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 59/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/pages/privacy-policy', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,657 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,657 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,657 INFO sqlalchemy.engine.Engine [insertmanyvalues 60/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/home-electronics', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,657 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 60/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/home-electronics', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,657 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,657 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,657 INFO sqlalchemy.engine.Engine [insertmanyvalues 61/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/epaal-protective-case-for-xiaomi-power-bank-4i-10000mah-22-5w-fast-charge', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,657 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 61/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/epaal-protective-case-for-xiaomi-power-bank-4i-10000mah-22-5w-fast-charge', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,658 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,658 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,658 INFO sqlalchemy.engine.Engine [insertmanyvalues 62/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/clearance-sale', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,658 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 62/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/clearance-sale', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,658 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,658 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,658 INFO sqlalchemy.engine.Engine [insertmanyvalues 63/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/led-lights-and-lamps', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,658 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 63/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/led-lights-and-lamps', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,658 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,658 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,658 INFO sqlalchemy.engine.Engine [insertmanyvalues 64/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/realme-watch-2-pro', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,658 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 64/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/realme-watch-2-pro', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,658 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,658 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,658 INFO sqlalchemy.engine.Engine [insertmanyvalues 65/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/pages/track-order', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,658 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 65/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/pages/track-order', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,658 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,658 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,658 INFO sqlalchemy.engine.Engine [insertmanyvalues 66/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/car-mobile-holders', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,658 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 66/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/car-mobile-holders', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,658 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,658 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,658 INFO sqlalchemy.engine.Engine [insertmanyvalues 67/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/policies/terms-of-service', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,658 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 67/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/policies/terms-of-service', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,658 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,658 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,658 INFO sqlalchemy.engine.Engine [insertmanyvalues 68/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/xiaomi-mi-band-6', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,658 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 68/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/xiaomi-mi-band-6', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,658 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,658 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,658 INFO sqlalchemy.engine.Engine [insertmanyvalues 69/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/xiaomi-mi-band-5', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,658 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 69/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/xiaomi-mi-band-5', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,658 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,658 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,659 INFO sqlalchemy.engine.Engine [insertmanyvalues 70/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/xiaomi-mi-band-4', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,659 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 70/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/xiaomi-mi-band-4', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,659 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,659 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,659 INFO sqlalchemy.engine.Engine [insertmanyvalues 71/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/xiaomi-mi-band-3', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,659 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 71/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/xiaomi-mi-band-3', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,659 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,659 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,659 INFO sqlalchemy.engine.Engine [insertmanyvalues 72/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/xiaomi-mi-band-2', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,659 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 72/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/xiaomi-mi-band-2', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,659 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,659 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,659 INFO sqlalchemy.engine.Engine [insertmanyvalues 73/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/computer-accessories', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,659 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 73/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/computer-accessories', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,659 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,659 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,659 INFO sqlalchemy.engine.Engine [insertmanyvalues 74/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/pages/faqs', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,659 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 74/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/pages/faqs', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,659 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,659 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,659 INFO sqlalchemy.engine.Engine [insertmanyvalues 75/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/epaal%C2%AE-19mm-magnetic-mesh-universal-strap-for-samsung-watch-3-45mm-samsung-gear-s3-classic-frontier-huawei-gt2-realme-s-s-pro', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,659 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 75/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/epaal%C2%AE-19mm-magnetic-mesh-universal-strap-for-samsung-watch-3-45mm-samsung-gear-s3-classic-frontier-huawei-gt2-realme-s-s-pro', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,659 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,659 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,659 INFO sqlalchemy.engine.Engine [insertmanyvalues 76/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/wpm@a7f653a7w82d20f99pe720974fm204fcef1/custom/web-pixel-shopify-custom-pixel@0420/sandbox/modern', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,659 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 76/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/wpm@a7f653a7w82d20f99pe720974fm204fcef1/custom/web-pixel-shopify-custom-pixel@0420/sandbox/modern', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,659 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,659 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,659 INFO sqlalchemy.engine.Engine [insertmanyvalues 77/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/bluetooth-earphones', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,659 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 77/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/bluetooth-earphones', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,660 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,660 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,660 INFO sqlalchemy.engine.Engine [insertmanyvalues 78/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/pack-of-3-20mm-plain-color-watch-straps-for-amazfit-bip-amazfit-gts-galaxy-watch-active-2-gear-s2-classic-samsung-gear-trendy-watch-straps#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,660 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 78/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/pack-of-3-20mm-plain-color-watch-straps-for-amazfit-bip-amazfit-gts-galaxy-watch-active-2-gear-s2-classic-samsung-gear-trendy-watch-straps#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,660 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,660 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,660 INFO sqlalchemy.engine.Engine [insertmanyvalues 79/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/epaal®-spiral-cable-protector-winder-sleeve-for-protecting-expensive-cables#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,660 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 79/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/epaal®-spiral-cable-protector-winder-sleeve-for-protecting-expensive-cables#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,660 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,660 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,660 INFO sqlalchemy.engine.Engine [insertmanyvalues 80/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/keychains', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,660 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 80/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/keychains', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,660 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,660 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,660 INFO sqlalchemy.engine.Engine [insertmanyvalues 81/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/epaal%C2%AE-plain-color-replacement-silicone-strap-for-honor-band-6', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,660 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 81/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/epaal%C2%AE-plain-color-replacement-silicone-strap-for-honor-band-6', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,660 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,660 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,660 INFO sqlalchemy.engine.Engine [insertmanyvalues 82/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/epaal%C2%AE-magnetic-usb-dock-charger-cable-for-realme-watch-s-only-1-meter-long', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,660 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 82/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/epaal%C2%AE-magnetic-usb-dock-charger-cable-for-realme-watch-s-only-1-meter-long', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,660 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,660 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,660 INFO sqlalchemy.engine.Engine [insertmanyvalues 83/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/livox-non-slip-push-up-dips-bar-stand-for-home-workout-exercise-equipment-handstand-for-men-women-muscle-hand-arm-exercise', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,660 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 83/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/livox-non-slip-push-up-dips-bar-stand-for-home-workout-exercise-equipment-handstand-for-men-women-muscle-hand-arm-exercise', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,660 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,660 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,660 INFO sqlalchemy.engine.Engine [insertmanyvalues 84/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/for-honor-band-4', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,660 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 84/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/for-honor-band-4', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,660 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,660 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,660 INFO sqlalchemy.engine.Engine [insertmanyvalues 85/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/realme-watch-s', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,660 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 85/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/realme-watch-s', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,661 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,661 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,661 INFO sqlalchemy.engine.Engine [insertmanyvalues 86/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/for-honor-band-5', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,661 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 86/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/for-honor-band-5', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,661 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,661 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,661 INFO sqlalchemy.engine.Engine [insertmanyvalues 87/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/19-mm-straps', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,661 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 87/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/collections/19-mm-straps', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,661 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,661 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,661 INFO sqlalchemy.engine.Engine [insertmanyvalues 88/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/epaal-selfie-stick-tripod-with-bluetooth-remote-black', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,661 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 88/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/products/epaal-selfie-stick-tripod-with-bluetooth-remote-black', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,661 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,661 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:49:55,661 INFO sqlalchemy.engine.Engine [insertmanyvalues 89/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/policies/refund-policy', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,661 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 89/89 (ordered; batch not supported)] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 'https://www.epaal.in/policies/refund-policy', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:49:55,662 INFO sqlalchemy.engine.Engine COMMIT
2025-07-28 00:49:55,662 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-28 00:49:55,675 - app.utils.website_url_processor - INFO - Successfully stored 89 URLs for scrape_request_ref_id: ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42
2025-07-28 00:49:55,675 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-28 00:49:55,675 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 00:49:55,676 INFO sqlalchemy.engine.Engine INSERT INTO mcc_analysis_gemini (website, scrape_request_ref_id, result_status, mcc_code, business_category, business_description, reasoning, created_at, started_at, completed_at, failed_at, last_updated, error_message, details, org_id, processing_status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-28 00:49:55,676 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_analysis_gemini (website, scrape_request_ref_id, result_status, mcc_code, business_category, business_description, reasoning, created_at, started_at, completed_at, failed_at, last_updated, error_message, details, org_id, processing_status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-28 00:49:55,676 INFO sqlalchemy.engine.Engine [generated in 0.00018s] ('https://www.epaal.in/', 'ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', None, None, None, None, None, '2025-07-28T00:49:55.675593Z', None, None, None, None, None, None, 'default', 'PENDING')
2025-07-28 00:49:55,676 - sqlalchemy.engine.Engine - INFO - [generated in 0.00018s] ('https://www.epaal.in/', 'ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', None, None, None, None, None, '2025-07-28T00:49:55.675593Z', None, None, None, None, None, None, 'default', 'PENDING')
2025-07-28 00:49:55,677 INFO sqlalchemy.engine.Engine COMMIT
2025-07-28 00:49:55,677 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-28 00:49:55,679 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-28 00:49:55,679 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 00:49:55,680 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.id = ?
2025-07-28 00:49:55,680 - sqlalchemy.engine.Engine - INFO - SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.id = ?
2025-07-28 00:49:55,680 INFO sqlalchemy.engine.Engine [generated in 0.00014s] (25,)
2025-07-28 00:49:55,680 - sqlalchemy.engine.Engine - INFO - [generated in 0.00014s] (25,)
2025-07-28 00:49:55,681 - app.routers.mcc_analysis - INFO - Created new MCC analysis with ID 25
2025-07-28 00:49:55,681 - app.routers.mcc_analysis - INFO - Background task queued for analysis 25
2025-07-28 00:49:55,681 INFO sqlalchemy.engine.Engine ROLLBACK
2025-07-28 00:49:55,681 - sqlalchemy.engine.Engine - INFO - ROLLBACK
[2025-07-28 00:49:55][request_middleware][NO_REF] INFO: Request completed: POST /mcc-analysis/ - 200
{
  "request_id": "41c2a7bf-2db2-40ef-a9ca-5d61431250f1",
  "status_code": 200,
  "response_time_ms": 53.91
}
INFO:     127.0.0.1:57492 - "POST /mcc-analysis/ HTTP/1.1" 200 OK
2025-07-28 00:49:56,989 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-28 00:49:56,989 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 00:49:56,990 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id AS mcc_analysis_gemini_id, mcc_analysis_gemini.website AS mcc_analysis_gemini_website, mcc_analysis_gemini.scrape_request_ref_id AS mcc_analysis_gemini_scrape_request_ref_id, mcc_analysis_gemini.result_status AS mcc_analysis_gemini_result_status, mcc_analysis_gemini.mcc_code AS mcc_analysis_gemini_mcc_code, mcc_analysis_gemini.business_category AS mcc_analysis_gemini_business_category, mcc_analysis_gemini.business_description AS mcc_analysis_gemini_business_description, mcc_analysis_gemini.reasoning AS mcc_analysis_gemini_reasoning, mcc_analysis_gemini.created_at AS mcc_analysis_gemini_created_at, mcc_analysis_gemini.started_at AS mcc_analysis_gemini_started_at, mcc_analysis_gemini.completed_at AS mcc_analysis_gemini_completed_at, mcc_analysis_gemini.failed_at AS mcc_analysis_gemini_failed_at, mcc_analysis_gemini.last_updated AS mcc_analysis_gemini_last_updated, mcc_analysis_gemini.error_message AS mcc_analysis_gemini_error_message, mcc_analysis_gemini.details AS mcc_analysis_gemini_details, mcc_analysis_gemini.org_id AS mcc_analysis_gemini_org_id, mcc_analysis_gemini.processing_status AS mcc_analysis_gemini_processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.id = ?
2025-07-28 00:49:56,990 - sqlalchemy.engine.Engine - INFO - SELECT mcc_analysis_gemini.id AS mcc_analysis_gemini_id, mcc_analysis_gemini.website AS mcc_analysis_gemini_website, mcc_analysis_gemini.scrape_request_ref_id AS mcc_analysis_gemini_scrape_request_ref_id, mcc_analysis_gemini.result_status AS mcc_analysis_gemini_result_status, mcc_analysis_gemini.mcc_code AS mcc_analysis_gemini_mcc_code, mcc_analysis_gemini.business_category AS mcc_analysis_gemini_business_category, mcc_analysis_gemini.business_description AS mcc_analysis_gemini_business_description, mcc_analysis_gemini.reasoning AS mcc_analysis_gemini_reasoning, mcc_analysis_gemini.created_at AS mcc_analysis_gemini_created_at, mcc_analysis_gemini.started_at AS mcc_analysis_gemini_started_at, mcc_analysis_gemini.completed_at AS mcc_analysis_gemini_completed_at, mcc_analysis_gemini.failed_at AS mcc_analysis_gemini_failed_at, mcc_analysis_gemini.last_updated AS mcc_analysis_gemini_last_updated, mcc_analysis_gemini.error_message AS mcc_analysis_gemini_error_message, mcc_analysis_gemini.details AS mcc_analysis_gemini_details, mcc_analysis_gemini.org_id AS mcc_analysis_gemini_org_id, mcc_analysis_gemini.processing_status AS mcc_analysis_gemini_processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.id = ?
2025-07-28 00:49:56,990 INFO sqlalchemy.engine.Engine [generated in 0.00016s] (25,)
2025-07-28 00:49:56,990 - sqlalchemy.engine.Engine - INFO - [generated in 0.00016s] (25,)
2025-07-28 00:49:56,992 INFO sqlalchemy.engine.Engine UPDATE mcc_analysis_gemini SET started_at=?, processing_status=? WHERE mcc_analysis_gemini.id = ?
2025-07-28 00:49:56,992 - sqlalchemy.engine.Engine - INFO - UPDATE mcc_analysis_gemini SET started_at=?, processing_status=? WHERE mcc_analysis_gemini.id = ?
2025-07-28 00:49:56,992 INFO sqlalchemy.engine.Engine [generated in 0.00016s] ('2025-07-28T00:49:56.991139Z', 'PROCESSING', 25)
2025-07-28 00:49:56,992 - sqlalchemy.engine.Engine - INFO - [generated in 0.00016s] ('2025-07-28T00:49:56.991139Z', 'PROCESSING', 25)
2025-07-28 00:49:56,992 INFO sqlalchemy.engine.Engine COMMIT
2025-07-28 00:49:56,992 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-28 00:49:57,004 - app.routers.mcc_analysis - INFO - Updated MccAnalysis status to PROCESSING
[2025-07-28 00:49:57][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Starting MCC analysis process
{
  "scrape_request_ref_id": "ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42",
  "org_id": "default"
}
[2025-07-28 00:49:57][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Step 1: Getting URLs data
[2025-07-28 00:49:57][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Checking if URLs exist in database
{
  "scrape_request_ref_id": "ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42"
}
2025-07-28 00:49:57,005 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-28 00:49:57,005 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 00:49:57,005 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-28 00:49:57,005 - sqlalchemy.engine.Engine - INFO - SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-28 00:49:57,005 INFO sqlalchemy.engine.Engine [cached since 1.364s ago] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42',)
2025-07-28 00:49:57,005 - sqlalchemy.engine.Engine - INFO - [cached since 1.364s ago] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42',)
2025-07-28 00:49:57,007 - app.utils.website_url_processor - INFO - Retrieved 89 URLs for scrape_request_ref_id: ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42
2025-07-28 00:49:57,007 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = ?
2025-07-28 00:49:57,007 - sqlalchemy.engine.Engine - INFO - SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = ?
2025-07-28 00:49:57,007 INFO sqlalchemy.engine.Engine [cached since 1.368s ago] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42',)
2025-07-28 00:49:57,007 - sqlalchemy.engine.Engine - INFO - [cached since 1.368s ago] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42',)
[2025-07-28 00:49:57][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: URLs found in database
{
  "website": "https://www.epaal.in/",
  "total_depths": 1,
  "total_urls": 89
}
[2025-07-28 00:49:57][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Successfully unpacked URL check result
{
  "urls_exist": true,
  "urls_data_type": "<class 'dict'>"
}
[2025-07-28 00:49:57][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Total parsed URLs found: 89
[2025-07-28 00:49:57][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Step 2: Preparing data for analysis
[2025-07-28 00:49:57][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Preparing data for MCC analysis
{
  "website": "https://www.epaal.in/"
}
[2025-07-28 00:49:57][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Got URLs by depth
{
  "depth_1_count": 89,
  "depth_2_count": 0,
  "scrape_request_ref_id": "ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42"
}
[2025-07-28 00:49:57][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Checking for existing classification results in database
2025-07-28 00:49:57,008 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-28 00:49:57,008 - sqlalchemy.engine.Engine - INFO - SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-28 00:49:57,008 INFO sqlalchemy.engine.Engine [cached since 1.366s ago] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42',)
2025-07-28 00:49:57,008 - sqlalchemy.engine.Engine - INFO - [cached since 1.366s ago] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42',)
2025-07-28 00:50:01,174 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
[2025-07-28 00:49:57][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Found 89 stored URL records
[2025-07-28 00:49:57][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Stored classification analysis
{
  "total_stored_urls": 89,
  "unreachable_count": 89,
  "classified_count": 0,
  "stored_categories": {
    "urls_not_reachable": 89
  }
}
[2025-07-28 00:49:57][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] WARNING: [RACE CONDITION FIX] All stored URLs marked unreachable - forcing fresh classification
{
  "total_urls": 89,
  "unreachable_urls": 89,
  "classified_urls": 0,
  "action": "FORCING_FRESH_CLASSIFICATION",
  "scrape_ref": "ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42",
  "fix_type": "RACE_CONDITION_PROTECTION",
  "time": "2025-07-28T00:49:57.009486"
}
[2025-07-28 00:49:57][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: No usable stored classification results found, proceeding with fresh API calls
[2025-07-28 00:49:57][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Starting soft classification of URLs
[2025-07-28 00:49:57][url_classification_ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Starting soft classification
{
  "website": "https://www.epaal.in/",
  "urls_depth_1_count": 89,
  "urls_depth_2_count": 0
}
[2025-07-28 00:49:57][url_classification_ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Dictionary for soft classification prepared
{
  "url_count": 89,
  "sample_urls": [
    "https://www.epaal.in/",
    "https://www.epaal.in/collections/earbud-case",
    "https://www.epaal.in/products/epaal-protective-case-for-xiaomi-power-bank-4i-20000mah-33w-power-bank"
  ]
}
[2025-07-28 00:49:57][test-analysis][NO_REF] INFO: Starting URL processing for model policy result
{
  "website": "https://www.epaal.in/",
  "total_urls": 89
}
[2025-07-28 00:49:57][test-analysis][NO_REF] INFO: Token calculation: system=265, base_user=988, available_for_urls=87747
[2025-07-28 00:49:57][test-analysis][NO_REF] INFO: Total URL tokens: 1902, Available: 87747
[2025-07-28 00:49:57][test-analysis][NO_REF] INFO: All URLs fit within token limit
[2025-07-28 00:49:57][test-analysis][NO_REF] INFO: Final URLs after token limiting
{
  "original_url_count": 89,
  "final_url_count": 89,
  "urls_trimmed": 0,
  "system_tokens": 265,
  "base_user_tokens": 988,
  "url_tokens": 1902,
  "final_total_tokens": 3155,
  "token_limit": 90000,
  "remaining_tokens": 86845
}
[2025-07-28 00:49:57][test-analysis][NO_REF] INFO: Final prompt verification
{
  "actual_prompt_tokens": 3597,
  "token_limit": 90000,
  "within_limit": true,
  "processing_time": 0.*****************
}
[2025-07-28 00:49:57][url_classification_ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Sending request to OpenAI API using model gpt-4o...
[2025-07-28 00:49:57][url_classification_ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Processing URLs for classification. This may take some time...
[2025-07-28 00:50:01][url_classification_ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Usage: CompletionUsage(completion_tokens=143, prompt_tokens=3577, total_tokens=3720, completion_tokens_details=CompletionTokensDetails(accepted_prediction_tokens=0, audio_tokens=0, reasoning_tokens=0, rejected_prediction_tokens=0), prompt_tokens_details=PromptTokensDetails(audio_tokens=0, cached_tokens=0))
[2025-07-28 00:50:02][url_classification_ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Soft classification response received
{
  "response_length": 455,
  "response_preview": "```json\n{\n    \"home_page\": [0],\n    \"about_us\": [3],\n    \"terms_and_condition\": [29, 66],\n    \"returns_cancellation_exchange\": [31, 55, 88],\n    \"privacy_policy\": [58],\n    \"shipping_delivery\": [12],\n..."
}
[2025-07-28 00:50:02][url_classification_ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Preparing data for model policy result
{
  "website": "https://www.epaal.in/",
  "dictionary_1_count": 89,
  "total_chars": 6365,
  "urls_sample": [
    "https://www.epaal.in/",
    "https://www.epaal.in/collections/earbud-case",
    "https://www.epaal.in/products/epaal-protective-case-for-xiaomi-power-bank-4i-20000mah-33w-power-bank"
  ]
}
[2025-07-28 00:50:02][url_classification_ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Got policy URLs classification
{
  "policy_urls": {
    "home_page": [
      0
    ],
    "about_us": [
      3
    ],
    "terms_and_condition": [
      29,
      66
    ],
    "returns_cancellation_exchange": [
      31,
      55,
      88
    ],
    "privacy_policy": [
      58
    ],
    "shipping_delivery": [
      12
    ],
    "contact_us": [
      38
    ],
    "products": [
      1,
      2,
      4,
      5,
      11
    ],
    "services": [],
    "catalogue": [
      23
    ],
    "instagram_page": [],
    "facebook_page": [],
    "twitter_page": [],
    "linkedin_page": [],
    "youtube_page": [],
    "pinterest_page": []
  },
  "categories_found": [
    "home_page",
    "about_us",
    "terms_and_condition",
    "returns_cancellation_exchange",
    "privacy_policy",
    "shipping_delivery",
    "contact_us",
    "products",
    "services",
    "catalogue",
    "instagram_page",
    "facebook_page",
    "twitter_page",
    "linkedin_page",
    "youtube_page",
    "pinterest_page"
  ]
}
[2025-07-28 00:50:02][url_classification_ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Completed soft classification
{
  "mcc_dict": {
    "home_page": [
      "https://www.epaal.in/"
    ],
    "about_us": [
      "https://www.epaal.in/pages/about-us"
    ],
    "terms_and_condition": [
      "https://www.epaal.in/pages/terms-conditions",
      "https://www.epaal.in/policies/terms-of-service"
    ],
    "returns_cancellation_exchange": [
      "https://www.epaal.in/pages/returns",
      "https://www.epaal.in/pages/cancellation-policy",
      "https://www.epaal.in/policies/refund-policy"
    ],
    "privacy_policy": [
      "https://www.epaal.in/pages/privacy-policy"
    ],
    "shipping_delivery": [
      "https://www.epaal.in/pages/shipping-policy"
    ],
    "contact_us": [
      "https://www.epaal.in/pages/contact-us"
    ],
    "products": [
      "https://www.epaal.in/collections/earbud-case",
      "https://www.epaal.in/products/epaal-protective-case-for-xiaomi-power-bank-4i-20000mah-33w-power-bank",
      "https://www.epaal.in/products/epaal-rugged-and-tough-bracelet-silicone-replacement-strap-for-xiaomi-mi-band-5#judgeme_product_reviews",
      "https://www.epaal.in/products/epaal-tpu-silicon-band-strap-for-huawei-honor-band-5-honor-band-4",
      "https://www.epaal.in/products/spiral-triple-color-2-pcs"
    ],
    "services": [],
    "catalogue": [
      "https://www.epaal.in/collections/new-collection"
    ],
    "instagram_page": [],
    "facebook_page": [],
    "twitter_page": [],
    "linkedin_page": [],
    "youtube_page": [],
    "pinterest_page": []
  },
  "total_classified_urls": 16,
  "priority_urls_count": 11
}
[2025-07-28 00:50:02][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Successfully unpacked soft classification result
{
  "output_df_type": "<class 'pandas.core.frame.DataFrame'>",
  "soft_classified_urls_type": "<class 'dict'>"
}
[2025-07-28 00:50:02][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Soft classification unreachable URL analysis
{
  "total_urls": 89,
  "unreachable_count": 0,
  "unreachable_ratio": "0/89",
  "unreachable_percentage": "0.0%",
  "urls_by_category": {
    "home_page": 1,
    "about_us": 1,
    "terms_and_condition": 2,
    "returns_cancellation_exchange": 3,
    "privacy_policy": 1,
    "shipping_delivery": 1,
    "contact_us": 1,
    "products": 5,
    "services": 0,
    "catalogue": 1,
    "instagram_page": 0,
    "facebook_page": 0,
    "twitter_page": 0,
    "linkedin_page": 0,
    "youtube_page": 0,
    "pinterest_page": 0
  }
}
[2025-07-28 00:50:02][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Soft classification completed
{
  "categories_found": [
    "home_page",
    "about_us",
    "terms_and_condition",
    "returns_cancellation_exchange",
    "privacy_policy",
    "shipping_delivery",
    "contact_us",
    "products",
    "services",
    "catalogue",
    "instagram_page",
    "facebook_page",
    "twitter_page",
    "linkedin_page",
    "youtube_page",
    "pinterest_page"
  ]
}2025-07-28 00:50:07,209 - google_genai.models - INFO - AFC is enabled with max remote calls: 20000.
2025-07-28 00:50:24,026 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent "HTTP/1.1 200 OK"
2025-07-28 00:50:24,031 - google_genai.models - INFO - AFC remote call 1 is done.

[2025-07-28 00:50:02][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Starting hard classification of URLs
[2025-07-28 00:50:02][url_classification_ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Starting hard classification verification for POLICY URLs only
{
  "website": "https://www.epaal.in/"
}
[2025-07-28 00:50:02][url_classification_ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Including policy category 'home_page' in hard classification
{
  "url_count": 1
}
[2025-07-28 00:50:02][url_classification_ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Including policy category 'about_us' in hard classification
{
  "url_count": 1
}
[2025-07-28 00:50:02][url_classification_ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Including policy category 'terms_and_condition' in hard classification
{
  "url_count": 2
}
[2025-07-28 00:50:02][url_classification_ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Including policy category 'returns_cancellation_exchange' in hard classification
{
  "url_count": 3
}
[2025-07-28 00:50:02][url_classification_ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Including policy category 'privacy_policy' in hard classification
{
  "url_count": 1
}
[2025-07-28 00:50:02][url_classification_ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Including policy category 'shipping_delivery' in hard classification
{
  "url_count": 1
}
[2025-07-28 00:50:02][url_classification_ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Including policy category 'contact_us' in hard classification
{
  "url_count": 1
}
[2025-07-28 00:50:02][url_classification_ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Including other category 'products' in hard classification
{
  "url_count": 5
}
[2025-07-28 00:50:02][url_classification_ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Including other category 'services' in hard classification
{
  "url_count": 0
}
[2025-07-28 00:50:02][url_classification_ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Including policy category 'catalogue' in hard classification
{
  "url_count": 1
}
[2025-07-28 00:50:02][url_classification_ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Skipping social media category 'instagram_page' from hard classification
{
  "url_count": 0,
  "reason": "social_media_uses_soft_classification_only"
}
[2025-07-28 00:50:02][url_classification_ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Skipping social media category 'facebook_page' from hard classification
{
  "url_count": 0,
  "reason": "social_media_uses_soft_classification_only"
}
[2025-07-28 00:50:02][url_classification_ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Skipping social media category 'twitter_page' from hard classification
{
  "url_count": 0,
  "reason": "social_media_uses_soft_classification_only"
}
[2025-07-28 00:50:02][url_classification_ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Skipping social media category 'linkedin_page' from hard classification
{
  "url_count": 0,
  "reason": "social_media_uses_soft_classification_only"
}
[2025-07-28 00:50:02][url_classification_ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Skipping social media category 'youtube_page' from hard classification
{
  "url_count": 0,
  "reason": "social_media_uses_soft_classification_only"
}
[2025-07-28 00:50:02][url_classification_ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Skipping social media category 'pinterest_page' from hard classification
{
  "url_count": 0,
  "reason": "social_media_uses_soft_classification_only"
}
[2025-07-28 00:50:02][test-analysis][NO_REF] INFO: Hard classification input
{
  "total_urls": 16,
  "website": "https://www.epaal.in/",
  "url_limit": 20
}
[2025-07-28 00:50:02][test-analysis][NO_REF] INFO: All URLs fit within hard classification limit
{
  "total_urls": 16,
  "limit": 20
}
[2025-07-28 00:50:02][gemini_optimizer_post_soft_classification][NO_REF] INFO: Optimizing Gemini call for task_type: post_soft_classification
{
  "received_task_type": "post_soft_classification"
}
[2025-07-28 00:50:07][post_soft_classification_unknown][NO_REF] INFO: Starting Gemini API call
{
  "model": "gemini-2.5-flash",
  "timeout": 120,
  "max_retries": 3,
  "prompt_length": 6162,
  "context": {
    "task_type": "post_soft_classification"
  }
}
[2025-07-28 00:50:07][post_soft_classification_unknown][NO_REF] INFO: Gemini API attempt 1/3
[2025-07-28 00:50:24][post_soft_classification_unknown][NO_REF] INFO: Gemini API Usage: cache_tokens_details=None cached_content_token_count=None candidates_token_count=581 candidates_tokens_details=None prompt_token_count=1602 prompt_tokens_details=[ModalityTokenCount(
  modality=<MediaModality.TEXT: 'TEXT'>,
  token_count=1602
)] thoughts_token_count=1394 tool_use_prompt_token_count=None tool_use_prompt_tokens_details=None total_token_count=3577 traffic_type=None
[2025-07-28 00:50:24][post_soft_classification_unknown][NO_REF] INFO: Gemini API call successful
{
  "attempt": 1,
  "response_length": 323,
  "finish_reason": "STOP"
}
[2025-07-28 00:50:25][url_classification_ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Gemini response received for hard classification
{
  "response_length": 323,
  "response_preview": "```json\n{\n    \"home_page\": [3],\n    \"about_us\": [11],\n    \"terms_and_condition\": [7],\n    \"returns_cancellation_exchange\": [0, 4],\n    \"privacy_policy\": [5],\n    \"shipping_delivery\": [6],\n    \"contact..."
}
[2025-07-28 00:50:25][url_classification_ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Parsing hard classification response
{
  "response": "```json\n{\n    \"home_page\": [3],\n    \"about_us\": [11],\n    \"terms_and_condition\": [7],\n    \"returns_cancellation_exchange\": [0, 4],\n    \"privacy_policy\": [5],\n    \"shipping_delivery\": [6],\n    \"contact_us\": [8],\n    \"catalogue\": [1, 9, 10, 12, 13, 15],\n    \"urls_not_reachable\": [],\n    \"Unreachable_via_tool\": [2, 14]\n}\n```"
}
[2025-07-28 00:50:25][url_classification_ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Verified indices dictionary created
{
  "indices_categories": [
    "home_page",
    "about_us",
    "terms_and_condition",
    "returns_cancellation_exchange",
    "privacy_policy",
    "shipping_delivery",
    "contact_us",
    "catalogue",
    "urls_not_reachable",
    "Unreachable_via_tool"
  ],
  "total_indices": 16
}
[2025-07-28 00:50:25][url_classification_ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Hard classification reachability results
{
  "unreachable_urls_count": 2,
  "reachable_urls_count": 14,
  "unreachable_urls": [
    2,
    14
  ],
  "website": "https://www.epaal.in/",
  "backup_flow_trigger": true
}
[2025-07-28 00:50:25][url_classification_ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Verified indices dictionary created
{
  "indices_categories": [
    "home_page",
    "about_us",
    "terms_and_condition",
    "returns_cancellation_exchange",
    "privacy_policy",
    "shipping_delivery",
    "contact_us",
    "catalogue",
    "urls_not_reachable",
    "Unreachable_via_tool"
  ],
  "total_indices": 16
}
[2025-07-28 00:50:25][url_classification_ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Verified URLs dictionary created with social media merged
{
  "verified_categories": [
    "home_page",
    "about_us",
    "terms_and_condition",
    "returns_cancellation_exchange",
    "privacy_policy",
    "shipping_delivery",
    "contact_us",
    "catalogue",
    "urls_not_reachable",
    "Unreachable_via_tool"
  ],
  "category_counts": {
    "home_page": 1,
    "about_us": 1,
    "terms_and_condition": 1,
    "returns_cancellation_exchange": 2,
    "privacy_policy": 1,
    "shipping_delivery": 1,
    "contact_us": 1,
    "catalogue": 6,
    "urls_not_reachable": 0,
    "Unreachable_via_tool": 2
  },
  "total_verified_urls": 16,
  "social_media_merged": true
}
[2025-07-28 00:50:25][url_classification_ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Completed hard classification verification with social media merge
{
  "verified_categories": [
    "home_page",
    "about_us",
    "terms_and_condition",
    "returns_cancellation_exchange",
    "privacy_policy",
    "shipping_delivery",
    "contact_us",
    "catalogue",
    "urls_not_reachable",
    "Unreachable_via_tool"
  ],
  "total_verified_urls": 16,
  "policy_hard_classified": true,
  "social_media_soft_classified": true
}
[2025-07-28 00:50:25][url_classification_ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Saving hard classification results to database
2025-07-28 00:50:25,033 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-28 00:50:25,033 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 00:50:25,033 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-28 00:50:25,033 - sqlalchemy.engine.Engine - INFO - SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-28 00:50:25,033 INFO sqlalchemy.engine.Engine [cached since 29.39s ago] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42',)
2025-07-28 00:50:25,033 - sqlalchemy.engine.Engine - INFO - [cached since 29.39s ago] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42',)
[2025-07-28 00:50:25][url_classification_ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Retrieved 89 URL records for hard classification update
2025-07-28 00:50:25,035 INFO sqlalchemy.engine.Engine UPDATE website_urls_gemini SET hard_class=? WHERE website_urls_gemini.id = ?
2025-07-28 00:50:25,035 - sqlalchemy.engine.Engine - INFO - UPDATE website_urls_gemini SET hard_class=? WHERE website_urls_gemini.id = ?
2025-07-28 00:50:25,035 INFO sqlalchemy.engine.Engine [generated in 0.00023s] [('["home_page"]', 1860), ('["catalogue"]', 1861), ('["catalogue"]', 1862), ('["about_us"]', 1863), ('["catalogue"]', 1864), ('["catalogue"]', 1865), ('["catalogue"]', 1871), ('["shipping_delivery"]', 1872)  ... displaying 10 of 16 total bound parameter sets ...  ('["Unreachable_via_tool"]', 1926), ('["Unreachable_via_tool"]', 1948)]
2025-07-28 00:50:25,035 - sqlalchemy.engine.Engine - INFO - [generated in 0.00023s] [('["home_page"]', 1860), ('["catalogue"]', 1861), ('["catalogue"]', 1862), ('["about_us"]', 1863), ('["catalogue"]', 1864), ('["catalogue"]', 1865), ('["catalogue"]', 1871), ('["shipping_delivery"]', 1872)  ... displaying 10 of 16 total bound parameter sets ...  ('["Unreachable_via_tool"]', 1926), ('["Unreachable_via_tool"]', 1948)]
2025-07-28 00:50:25,036 INFO sqlalchemy.engine.Engine COMMIT
2025-07-28 00:50:25,036 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-07-28 00:50:25][url_classification_ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Updated 16 database records with hard classification
[2025-07-28 00:50:25][url_classification_ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Hard classification summary: 16 total URLs across 10 categories
[2025-07-28 00:50:25][url_classification_ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Hard classification category breakdown: {'home_page': 1, 'about_us': 1, 'terms_and_condition': 1, 'returns_cancellation_exchange': 2, 'privacy_policy': 1, 'shipping_delivery': 1, 'contact_us': 1, 'catalogue': 6, 'urls_not_reachable': 0, 'Unreachable_via_tool': 2}
[2025-07-28 00:50:25][url_classification_ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Successfully saved hard classification results to database
[2025-07-28 00:50:25][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Hard classification completed
{
  "categories_found": [
    "home_page",
    "about_us",
    "terms_and_condition",
    "returns_cancellation_exchange",
    "privacy_policy",
    "shipping_delivery",
    "contact_us",
    "catalogue",
    "urls_not_reachable",
    "Unreachable_via_tool"
  ]
}
[2025-07-28 00:50:25][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Hard classification results - detailed breakdown
{
  "category_counts": {
    "home_page": 1,
    "about_us": 1,
    "terms_and_condition": 1,
    "returns_cancellation_exchange": 2,
    "privacy_policy": 1,
    "shipping_delivery": 1,
    "contact_us": 1,
    "catalogue": 6,
    "urls_not_reachable": 0,
    "Unreachable_via_tool": 2
  },
  "total_reachable_urls": 16,
  "priority_reachable_urls": 8,
  "total_unreachable_urls": 0,
  "unreachable_percentage": "0.0%"
}
[2025-07-28 00:50:25][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Some URLs are reachable - continuing with normal flow
{
  "total_reachable_urls": 16,
  "priority_urls_found": 8,
  "decision": "NORMAL_FLOW_CONTINUES",
  "reachable_categories": [
    "home_page",
    "about_us",
    "terms_and_condition",
    "returns_cancellation_exchange",
    "privacy_policy",
    "shipping_delivery",
    "contact_us",
    "catalogue"
  ]
}
[2025-07-28 00:50:25][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Starting priority URL filtering
{
  "input_categories": [
    "home_page",
    "about_us",
    "terms_and_condition",
    "returns_cancellation_exchange",
    "privacy_policy",
    "shipping_delivery",
    "contact_us",
    "catalogue",
    "urls_not_reachable",
    "Unreachable_via_tool"
  ],
  "priority_categories_defined": [
    "about_us",
    "catalogue",
    "products",
    "home_page"
  ]
}
[2025-07-28 00:50:25][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Filtering priority URLs for MCC analysis - input analysis
{
  "total_categories": 10,
  "input_category_counts": {
    "home_page": 1,
    "about_us": 1,
    "terms_and_condition": 1,
    "returns_cancellation_exchange": 2,
    "privacy_policy": 1,
    "shipping_delivery": 1,
    "contact_us": 1,
    "catalogue": 6,
    "urls_not_reachable": 0,
    "Unreachable_via_tool": 2
  },
  "priority_categories": [
    "about_us",
    "catalogue",
    "products",
    "home_page"
  ],
  "max_urls_for_mcc": 18
}
[2025-07-28 00:50:25][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: URLs per category calculated: 18
[2025-07-28 00:50:25][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] DEBUG: Processing category 'home_page': 1 URLs
{
  "category": "home_page",
  "url_count": 1,
  "is_priority": true,
  "has_urls": true
}
[2025-07-28 00:50:25][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: ✅ Added 1 URLs for priority category 'home_page'
{
  "category": "home_page",
  "added_urls": 1,
  "total_available": 1,
  "urls_per_category_limit": 18
}
[2025-07-28 00:50:25][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] DEBUG: Processing category 'about_us': 1 URLs
{
  "category": "about_us",
  "url_count": 1,
  "is_priority": true,
  "has_urls": true
}
[2025-07-28 00:50:25][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: ✅ Added 1 URLs for priority category 'about_us'
{
  "category": "about_us",
  "added_urls": 1,
  "total_available": 1,
  "urls_per_category_limit": 18
}
[2025-07-28 00:50:25][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] DEBUG: Processing category 'terms_and_condition': 1 URLs
{
  "category": "terms_and_condition",
  "url_count": 1,
  "is_priority": false,
  "has_urls": true
}
[2025-07-28 00:50:25][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] DEBUG: Processing category 'returns_cancellation_exchange': 2 URLs
{
  "category": "returns_cancellation_exchange",
  "url_count": 2,
  "is_priority": false,
  "has_urls": true
}
[2025-07-28 00:50:25][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] DEBUG: Processing category 'privacy_policy': 1 URLs
{
  "category": "privacy_policy",
  "url_count": 1,
  "is_priority": false,
  "has_urls": true
}
[2025-07-28 00:50:25][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] DEBUG: Processing category 'shipping_delivery': 1 URLs
{
  "category": "shipping_delivery",
  "url_count": 1,
  "is_priority": false,
  "has_urls": true
}
[2025-07-28 00:50:25][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] DEBUG: Processing category 'contact_us': 1 URLs
{
  "category": "contact_us",
  "url_count": 1,
  "is_priority": false,
  "has_urls": true
}
[2025-07-28 00:50:25][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] DEBUG: Processing category 'catalogue': 6 URLs
{
  "category": "catalogue",
  "url_count": 6,
  "is_priority": true,
  "has_urls": true
}
[2025-07-28 00:50:25][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: ✅ Added 6 URLs for priority category 'catalogue'
{
  "category": "catalogue",
  "added_urls": 6,
  "total_available": 6,
  "urls_per_category_limit": 18
}
[2025-07-28 00:50:25][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] DEBUG: Processing category 'urls_not_reachable': 0 URLs
{
  "category": "urls_not_reachable",
  "url_count": 0,
  "is_priority": false,
  "has_urls": false
}
[2025-07-28 00:50:25][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] DEBUG: Processing category 'Unreachable_via_tool': 2 URLs
{
  "category": "Unreachable_via_tool",
  "url_count": 2,
  "is_priority": false,
  "has_urls": true
}
[2025-07-28 00:50:25][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Priority URLs filtered
{
  "priority_categories": [
    "home_page",
    "about_us",
    "catalogue"
  ],
  "total_priority_urls": 8,
  "fallback_applied": false
}
[2025-07-28 00:50:25][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Priority URL filtering completed
{
  "priority_url_counts": {
    "home_page": 1,
    "about_us": 1,
    "catalogue": 6
  },
  "total_priority_urls": 8
}
[2025-07-28 00:50:25][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Data preparation completed
{
  "total_classified_urls": 16,
  "priority_urls_count": 8
}
[2025-07-28 00:50:25][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Successfully unpacked data preparation result
{
  "classified_urls_type": "<class 'dict'>",
  "priority_urls_type": "<class 'dict'>",
  "classified_urls_is_none": false,
  "priority_urls_is_none": false
}
[2025-07-28 00:50:25][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Step 2 completed in 28.04 seconds
[2025-07-28 00:50:25][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Step 3: Starting MCC classification
{
  "website": "https://www.epaal.in/",
  "priority_url_categories": [
    "home_page",
    "about_us",
    "catalogue"
  ],
  "total_priority_urls": 8
}
[2025-07-28 00:50:25][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Starting MCC classification
{
  "website": "https://www.epaal.in/",
  "priority_categories": [
    "home_page",
    "about_us",
    "catalogue"
  ]
}
[2025-07-28 00:50:25][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: 🔍 Extracting text content from priority URLs for analysis
[2025-07-28 00:50:25][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: 📄 Extracting text from home_page: https://www.epaal.in/2025-07-28 00:51:58,862 - app.routers.mcc_analysis - INFO - Processing MCC analysis request for https://bakingcart.com/ with ref_id b07e8022-c849-4042-a353-c99216bb5d74

[2025-07-28 00:50:25][text_extraction][NO_REF] INFO: Starting text extraction
{
  "url": "https://www.epaal.in/",
  "timeout": 60
}
[2025-07-28 00:50:35][text_extraction][NO_REF] WARNING: JavaScript security challenge detected
{
  "url": "https://www.epaal.in/"
}
[2025-07-28 00:50:35][text_extraction][NO_REF] WARNING: Page appears to be blocked with direct connection
{
  "url": "https://www.epaal.in/",
  "retry": 0
}
[2025-07-28 00:50:35][text_extraction][NO_REF] INFO: Retrying direct connection in 1.50 seconds
[2025-07-28 00:50:45][text_extraction][NO_REF] WARNING: JavaScript security challenge detected
{
  "url": "https://www.epaal.in/"
}
[2025-07-28 00:50:45][text_extraction][NO_REF] WARNING: Page appears to be blocked with direct connection
{
  "url": "https://www.epaal.in/",
  "retry": 1
}
[2025-07-28 00:50:46][text_extraction][NO_REF] INFO: Attempting proxy connection for text extraction as fallback
[2025-07-28 00:50:46][text_extraction][NO_REF] INFO: Using webshare proxy with valid credentials
[2025-07-28 00:51:22][text_extraction][NO_REF] ERROR: Failed to navigate to URL with proxy for text extraction (timeout)
{
  "error": "{'error': {'error': 'Page.goto: Timeout 36000ms exceeded.\\nCall log:\\n  - navigating to \"https://www.epaal.in/\", waiting until \"load\"\\n', 'url': 'https://www.epaal.in/', 'retry': 0}}",
  "error_type": "dict"
}
Traceback for analysis text_extraction:
Error printing traceback: 'dict' object has no attribute '__traceback__'
[2025-07-28 00:51:22][text_extraction][NO_REF] ERROR: Failed to access https://www.epaal.in/ for text extraction with both direct and proxy connections
[2025-07-28 00:51:22][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] WARNING: ⚠️ Playwright extraction insufficient for home_page, trying requests method
[2025-07-28 00:51:23][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: ✅ Text extracted from home_page: 5000 characters
[2025-07-28 00:51:23][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: 📄 Extracting text from about_us: https://www.epaal.in/pages/about-us
[2025-07-28 00:51:23][text_extraction][NO_REF] INFO: Starting text extraction
{
  "url": "https://www.epaal.in/pages/about-us",
  "timeout": 60
}
[2025-07-28 00:51:31][text_extraction][NO_REF] WARNING: JavaScript security challenge detected
{
  "url": "https://www.epaal.in/pages/about-us"
}
[2025-07-28 00:51:31][text_extraction][NO_REF] WARNING: Page appears to be blocked with direct connection
{
  "url": "https://www.epaal.in/pages/about-us",
  "retry": 0
}
[2025-07-28 00:51:31][text_extraction][NO_REF] INFO: Retrying direct connection in 1.50 seconds
[2025-07-28 00:51:41][text_extraction][NO_REF] WARNING: JavaScript security challenge detected
{
  "url": "https://www.epaal.in/pages/about-us"
}
[2025-07-28 00:51:41][text_extraction][NO_REF] WARNING: Page appears to be blocked with direct connection
{
  "url": "https://www.epaal.in/pages/about-us",
  "retry": 1
}
[2025-07-28 00:51:41][text_extraction][NO_REF] INFO: Attempting proxy connection for text extraction as fallback
[2025-07-28 00:51:41][text_extraction][NO_REF] INFO: Using webshare proxy with valid credentials
[2025-07-28 00:51:58][request_middleware][NO_REF] INFO: Request started: POST /mcc-analysis/
{
  "request_id": "32fb9c9e-18f0-4c21-ac43-65041e81f4d9",
  "client_ip": "127.0.0.1",
  "user_agent": "Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0",
  "query_params": null,
  "headers": {
    "host": "127.0.0.1:9000",
    "user-agent": "Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0",
    "accept": "application/json",
    "accept-language": "en-US,en;q=0.5",
    "accept-encoding": "gzip, deflate, br, zstd",
    "referer": "http://127.0.0.1:9000/docs",
    "content-type": "application/json",
    "content-length": "10680",
    "origin": "http://127.0.0.1:9000",
    "connection": "keep-alive",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "priority": "u=0"
  },
  "url_full": "http://127.0.0.1:9000/mcc-analysis/"
}
2025-07-28 00:51:58,863 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-28 00:51:58,863 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 00:51:58,863 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = ?
2025-07-28 00:51:58,863 - sqlalchemy.engine.Engine - INFO - SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = ?
2025-07-28 00:51:58,863 INFO sqlalchemy.engine.Engine [cached since 123.2s ago] ('b07e8022-c849-4042-a353-c99216bb5d74',)
2025-07-28 00:51:58,863 - sqlalchemy.engine.Engine - INFO - [cached since 123.2s ago] ('b07e8022-c849-4042-a353-c99216bb5d74',)
2025-07-28 00:51:58,863 - app.routers.mcc_analysis - INFO - MCC analysis already exists with ID 10
2025-07-28 00:51:58,864 INFO sqlalchemy.engine.Engine ROLLBACK
2025-07-28 00:51:58,864 - sqlalchemy.engine.Engine - INFO - ROLLBACK
[2025-07-28 00:51:58][request_middleware][NO_REF] INFO: Request completed: POST /mcc-analysis/ - 200
{
  "request_id": "32fb9c9e-18f0-4c21-ac43-65041e81f4d9",
  "status_code": 200,
  "response_time_ms": 2.81
}
INFO:     127.0.0.1:44298 - "POST /mcc-analysis/ HTTP/1.1" 200 OK
2025-07-28 00:52:05,491 - app.routers.mcc_analysis - INFO - Processing MCC analysis request for https://bakingcart.com/ with ref_id b07e8022-c849-4042-a353-c99216bbasdfasdf5d74
[2025-07-28 00:52:05][request_middleware][NO_REF] INFO: Request started: POST /mcc-analysis/
{
  "request_id": "d2015e95-9460-4ca6-829c-1c7863d46508",
  "client_ip": "127.0.0.1",
  "user_agent": "Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0",
  "query_params": null,
  "headers": {
    "host": "127.0.0.1:9000",
    "user-agent": "Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0",
    "accept": "application/json",
    "accept-language": "en-US,en;q=0.5",
    "accept-encoding": "gzip, deflate, br, zstd",
    "referer": "http://127.0.0.1:9000/docs",
    "content-type": "application/json",
    "content-length": "10688",
    "origin": "http://127.0.0.1:9000",
    "connection": "keep-alive",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "priority": "u=0"
  },
  "url_full": "http://127.0.0.1:9000/mcc-analysis/"
}
2025-07-28 00:52:05,492 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-28 00:52:05,492 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 00:52:05,492 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = ?
2025-07-28 00:52:05,492 - sqlalchemy.engine.Engine - INFO - SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = ?
2025-07-28 00:52:05,492 INFO sqlalchemy.engine.Engine [cached since 129.9s ago] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74',)
2025-07-28 00:52:05,492 - sqlalchemy.engine.Engine - INFO - [cached since 129.9s ago] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74',)
2025-07-28 00:52:05,492 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-28 00:52:05,492 - sqlalchemy.engine.Engine - INFO - SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-28 00:52:05,492 INFO sqlalchemy.engine.Engine [cached since 129.9s ago] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74',)
2025-07-28 00:52:05,492 - sqlalchemy.engine.Engine - INFO - [cached since 129.9s ago] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74',)
2025-07-28 00:52:05,506 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,506 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,506 INFO sqlalchemy.engine.Engine [cached since 129.9s ago (insertmanyvalues) 1/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,506 - sqlalchemy.engine.Engine - INFO - [cached since 129.9s ago (insertmanyvalues) 1/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,507 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,507 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,507 INFO sqlalchemy.engine.Engine [insertmanyvalues 2/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-passion-fruit', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,507 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 2/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-passion-fruit', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,507 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,507 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,507 INFO sqlalchemy.engine.Engine [insertmanyvalues 3/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/apyura-premium-spread/products/orange-belgium', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,507 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 3/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/apyura-premium-spread/products/orange-belgium', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,507 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,507 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,507 INFO sqlalchemy.engine.Engine [insertmanyvalues 4/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-mint-2', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,507 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 4/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-mint-2', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,507 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,507 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,507 INFO sqlalchemy.engine.Engine [insertmanyvalues 5/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/pages/about-us', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,507 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 5/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/pages/about-us', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,507 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,507 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,507 INFO sqlalchemy.engine.Engine [insertmanyvalues 6/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-rose', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,507 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 6/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-rose', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,507 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,507 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,507 INFO sqlalchemy.engine.Engine [insertmanyvalues 7/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-hazelnut', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,507 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 7/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-hazelnut', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,507 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,507 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,507 INFO sqlalchemy.engine.Engine [insertmanyvalues 8/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-orange', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,507 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 8/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-orange', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,507 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,507 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,507 INFO sqlalchemy.engine.Engine [insertmanyvalues 9/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/all-purpose-flour/products/wilton-1-m', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,507 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 9/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/all-purpose-flour/products/wilton-1-m', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,507 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,507 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,507 INFO sqlalchemy.engine.Engine [insertmanyvalues 10/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-rum', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,507 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 10/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-rum', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,507 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,507 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,508 INFO sqlalchemy.engine.Engine [insertmanyvalues 11/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-crackle', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,508 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 11/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-crackle', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,508 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,508 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,508 INFO sqlalchemy.engine.Engine [insertmanyvalues 12/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/products/titamisu', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,508 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 12/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/products/titamisu', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,508 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,508 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,508 INFO sqlalchemy.engine.Engine [insertmanyvalues 13/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/all-purpose-flour', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,508 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 13/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/all-purpose-flour', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,508 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,508 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,508 INFO sqlalchemy.engine.Engine [insertmanyvalues 14/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/products/wilton-1-m', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,508 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 14/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/products/wilton-1-m', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,508 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,508 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,508 INFO sqlalchemy.engine.Engine [insertmanyvalues 15/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/apyura-premium-spread/products/apyura-bubblegum', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,508 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 15/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/apyura-premium-spread/products/apyura-bubblegum', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,508 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,508 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,508 INFO sqlalchemy.engine.Engine [insertmanyvalues 16/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,508 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 16/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,508 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,508 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,508 INFO sqlalchemy.engine.Engine [insertmanyvalues 17/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/products/al-barakah-premium-arabian-date-powder-250gram-date-sugar-natural-sweetener-perfect-for-desserts-baking-and-smoothie-sugar-substitute', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,508 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 17/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/products/al-barakah-premium-arabian-date-powder-250gram-date-sugar-natural-sweetener-perfect-for-desserts-baking-and-smoothie-sugar-substitute', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,508 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,508 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,508 INFO sqlalchemy.engine.Engine [insertmanyvalues 18/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/apyura-premium-spread', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,508 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 18/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/apyura-premium-spread', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,508 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,508 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,508 INFO sqlalchemy.engine.Engine [insertmanyvalues 19/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/#', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,508 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 19/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/#', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,508 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,508 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,508 INFO sqlalchemy.engine.Engine [insertmanyvalues 20/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-mango', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,508 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 20/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-mango', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,508 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,508 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,508 INFO sqlalchemy.engine.Engine [insertmanyvalues 21/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-rum', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,508 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 21/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-rum', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,508 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,508 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,508 INFO sqlalchemy.engine.Engine [insertmanyvalues 22/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-cardamom', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,508 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 22/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-cardamom', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,508 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,508 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,508 INFO sqlalchemy.engine.Engine [insertmanyvalues 23/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/wilton-1-m', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,508 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 23/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/wilton-1-m', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,509 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,509 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,509 INFO sqlalchemy.engine.Engine [insertmanyvalues 24/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/products/101-piping-nozzle', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,509 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 24/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/products/101-piping-nozzle', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,509 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,509 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,509 INFO sqlalchemy.engine.Engine [insertmanyvalues 25/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-crackle', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,509 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 25/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-crackle', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,509 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,509 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,509 INFO sqlalchemy.engine.Engine [insertmanyvalues 26/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-hazelnut', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,509 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 26/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-hazelnut', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,509 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,509 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,509 INFO sqlalchemy.engine.Engine [insertmanyvalues 27/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/rasmalai', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,509 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 27/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/rasmalai', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,509 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,509 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,509 INFO sqlalchemy.engine.Engine [insertmanyvalues 28/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-litchi', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,509 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 28/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-litchi', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,509 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,509 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,509 INFO sqlalchemy.engine.Engine [insertmanyvalues 29/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/all-purpose-flour/products/101-piping-nozzle', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,509 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 29/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/all-purpose-flour/products/101-piping-nozzle', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,509 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,509 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,509 INFO sqlalchemy.engine.Engine [insertmanyvalues 30/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/all-collections', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,509 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 30/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/all-collections', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,509 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,509 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,509 INFO sqlalchemy.engine.Engine [insertmanyvalues 31/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/christmas-tree-cookie-cutter-set-3-pieces-aluminium-xmas-tree-cookie-cutters-with-recipe-booklet-biscuit-molds-for-chirstmas-day-party-decorations-diy-gift-family-kids-baking-tools', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,509 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 31/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/christmas-tree-cookie-cutter-set-3-pieces-aluminium-xmas-tree-cookie-cutters-with-recipe-booklet-biscuit-molds-for-chirstmas-day-party-decorations-diy-gift-family-kids-baking-tools', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,509 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,509 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,509 INFO sqlalchemy.engine.Engine [insertmanyvalues 32/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-vanilla', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,509 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 32/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-vanilla', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,509 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,509 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,509 INFO sqlalchemy.engine.Engine [insertmanyvalues 33/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/products/apyura-bubblegum', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,509 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 33/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/products/apyura-bubblegum', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,509 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,509 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,509 INFO sqlalchemy.engine.Engine [insertmanyvalues 34/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-blueberry', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,509 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 34/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-blueberry', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,509 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,509 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,509 INFO sqlalchemy.engine.Engine [insertmanyvalues 35/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/products/marshmallow-cute-teddy-10-pcs-packing', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,509 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 35/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/products/marshmallow-cute-teddy-10-pcs-packing', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,510 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,510 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,510 INFO sqlalchemy.engine.Engine [insertmanyvalues 36/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/titamisu', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,510 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 36/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/titamisu', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,510 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,510 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,510 INFO sqlalchemy.engine.Engine [insertmanyvalues 37/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-pineapple', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,510 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 37/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-pineapple', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,510 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,510 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,510 INFO sqlalchemy.engine.Engine [insertmanyvalues 38/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/fab-oil-soluble-strawberr-y', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,510 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 38/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/fab-oil-soluble-strawberr-y', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,510 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,510 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,510 INFO sqlalchemy.engine.Engine [insertmanyvalues 39/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/all-purpose-flour/products/star-cookie-cutter-set-5-pcs-stainless-steel-five-pointed-star-biscuit-molds-fondant-cake-cookie-cutter-set-pastry-mold-for-3d-christmas-tree-linzer-cookies', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,510 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 39/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/all-purpose-flour/products/star-cookie-cutter-set-5-pcs-stainless-steel-five-pointed-star-biscuit-molds-fondant-cake-cookie-cutter-set-pastry-mold-for-3d-christmas-tree-linzer-cookies', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,510 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,510 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,510 INFO sqlalchemy.engine.Engine [insertmanyvalues 40/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/products/rasmalai', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,510 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 40/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/products/rasmalai', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,510 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,510 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,510 INFO sqlalchemy.engine.Engine [insertmanyvalues 41/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-black-currant', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,510 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 41/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-black-currant', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,510 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,510 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,510 INFO sqlalchemy.engine.Engine [insertmanyvalues 42/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/all-purpose-flour/products/christmas-tree-cookie-cutter-set-3-pieces-aluminium-xmas-tree-cookie-cutters-with-recipe-booklet-biscuit-molds-for-chirstmas-day-party-decorations-diy-gift-family-kids-baking-tools', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,510 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 42/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/all-purpose-flour/products/christmas-tree-cookie-cutter-set-3-pieces-aluminium-xmas-tree-cookie-cutters-with-recipe-booklet-biscuit-molds-for-chirstmas-day-party-decorations-diy-gift-family-kids-baking-tools', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,510 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,510 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,510 INFO sqlalchemy.engine.Engine [insertmanyvalues 43/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/apyura-bubblegum', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,510 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 43/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/apyura-bubblegum', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,510 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,510 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,510 INFO sqlalchemy.engine.Engine [insertmanyvalues 44/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/pistachio', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,510 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 44/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/pistachio', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,510 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,510 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,510 INFO sqlalchemy.engine.Engine [insertmanyvalues 45/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/van-houten-dark-chocolate-couverture-46-5-cocoa-1-kg', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,510 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 45/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/van-houten-dark-chocolate-couverture-46-5-cocoa-1-kg', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,510 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,510 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,510 INFO sqlalchemy.engine.Engine [insertmanyvalues 46/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/van-houten-cocoa-powder-1kg-medium-brown-original-dutch-processed-unsweetened-100-cocoa-beans', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,510 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 46/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/van-houten-cocoa-powder-1kg-medium-brown-original-dutch-processed-unsweetened-100-cocoa-beans', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,510 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,510 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,510 INFO sqlalchemy.engine.Engine [insertmanyvalues 47/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-mango', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,510 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 47/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-mango', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,511 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,511 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,511 INFO sqlalchemy.engine.Engine [insertmanyvalues 48/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/star-cookie-cutter-set-5-pcs-stainless-steel-five-pointed-star-biscuit-molds-fondant-cake-cookie-cutter-set-pastry-mold-for-3d-christmas-tree-linzer-cookies', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,511 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 48/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/star-cookie-cutter-set-5-pcs-stainless-steel-five-pointed-star-biscuit-molds-fondant-cake-cookie-cutter-set-pastry-mold-for-3d-christmas-tree-linzer-cookies', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,511 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,511 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,511 INFO sqlalchemy.engine.Engine [insertmanyvalues 49/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/products/pistachio', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,511 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 49/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/products/pistachio', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,511 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,511 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,511 INFO sqlalchemy.engine.Engine [insertmanyvalues 50/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-guava', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,511 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 50/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-guava', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,511 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,511 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,511 INFO sqlalchemy.engine.Engine [insertmanyvalues 51/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/all-purpose-flour/products/van-houten-cocoa-powder-1kg-medium-brown-original-dutch-processed-unsweetened-100-cocoa-beans', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,511 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 51/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/all-purpose-flour/products/van-houten-cocoa-powder-1kg-medium-brown-original-dutch-processed-unsweetened-100-cocoa-beans', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,511 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,511 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,511 INFO sqlalchemy.engine.Engine [insertmanyvalues 52/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/apyura-premium-spread/products/rasmalai', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,511 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 52/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/apyura-premium-spread/products/rasmalai', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,511 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,511 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,511 INFO sqlalchemy.engine.Engine [insertmanyvalues 53/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-natural-extracts', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,511 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 53/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-natural-extracts', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,511 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,511 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,511 INFO sqlalchemy.engine.Engine [insertmanyvalues 54/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-irish-cream-1', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,511 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 54/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-irish-cream-1', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,511 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,511 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,511 INFO sqlalchemy.engine.Engine [insertmanyvalues 55/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-kewra', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,511 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 55/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-kewra', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,511 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,511 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,512 INFO sqlalchemy.engine.Engine [insertmanyvalues 56/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/fab-oil-soluble-saffron', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,512 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 56/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/fab-oil-soluble-saffron', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,512 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,512 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,512 INFO sqlalchemy.engine.Engine [insertmanyvalues 57/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-guava', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,512 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 57/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-guava', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,512 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,512 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,512 INFO sqlalchemy.engine.Engine [insertmanyvalues 58/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/cookie-and-crunch', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,512 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 58/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/cookie-and-crunch', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,512 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,512 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,512 INFO sqlalchemy.engine.Engine [insertmanyvalues 59/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,512 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 59/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,512 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,512 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,512 INFO sqlalchemy.engine.Engine [insertmanyvalues 60/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-mix-fruit', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,512 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 60/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-mix-fruit', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,512 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,512 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,512 INFO sqlalchemy.engine.Engine [insertmanyvalues 61/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/apyura-premium-spread/products/pistachio', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,512 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 61/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/apyura-premium-spread/products/pistachio', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,512 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,512 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,512 INFO sqlalchemy.engine.Engine [insertmanyvalues 62/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/lady-finger-tiramisu-400gms', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,512 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 62/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/lady-finger-tiramisu-400gms', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,512 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,512 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,512 INFO sqlalchemy.engine.Engine [insertmanyvalues 63/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-paan', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,512 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 63/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-paan', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,512 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,512 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,512 INFO sqlalchemy.engine.Engine [insertmanyvalues 64/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-black-currant', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,512 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 64/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-black-currant', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,512 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,512 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,512 INFO sqlalchemy.engine.Engine [insertmanyvalues 65/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/products/cookie-and-crunch', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,512 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 65/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/products/cookie-and-crunch', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,512 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,512 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,512 INFO sqlalchemy.engine.Engine [insertmanyvalues 66/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/search', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,512 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 66/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/search', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,512 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,512 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,512 INFO sqlalchemy.engine.Engine [insertmanyvalues 67/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/all-purpose-flour/products/lady-finger-tiramisu-400gms', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,512 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 67/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/all-purpose-flour/products/lady-finger-tiramisu-400gms', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,513 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,513 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,513 INFO sqlalchemy.engine.Engine [insertmanyvalues 68/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/cdn/shop/t/2/assets/logo.png?v=116277239867421797551702904462', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,513 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 68/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/cdn/shop/t/2/assets/logo.png?v=116277239867421797551702904462', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,513 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,513 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,513 INFO sqlalchemy.engine.Engine [insertmanyvalues 69/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/fab-oil-soluble-strawberr-y', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,513 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 69/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/fab-oil-soluble-strawberr-y', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,513 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,513 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,513 INFO sqlalchemy.engine.Engine [insertmanyvalues 70/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/fab-oil-soluble-vodka', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,513 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 70/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/fab-oil-soluble-vodka', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,513 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,513 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,513 INFO sqlalchemy.engine.Engine [insertmanyvalues 71/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/cdn', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,513 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 71/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/cdn', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,513 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,513 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,513 INFO sqlalchemy.engine.Engine [insertmanyvalues 72/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/apyura-premium-spread/products/titamisu', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,513 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 72/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/apyura-premium-spread/products/titamisu', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,513 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,513 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,513 INFO sqlalchemy.engine.Engine [insertmanyvalues 73/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-vintage-vanilla-series-water-soluble', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,513 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 73/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-vintage-vanilla-series-water-soluble', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,513 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,513 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,513 INFO sqlalchemy.engine.Engine [insertmanyvalues 74/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/all-purpose-flour/products/van-houten-white-chocolate-couverture-29-3-cocoa-1-kg', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,513 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 74/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/all-purpose-flour/products/van-houten-white-chocolate-couverture-29-3-cocoa-1-kg', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,513 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,513 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,513 INFO sqlalchemy.engine.Engine [insertmanyvalues 75/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/products/van-houten-cocoa-powder-1kg-medium-brown-original-dutch-processed-unsweetened-100-cocoa-beans', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,513 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 75/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/products/van-houten-cocoa-powder-1kg-medium-brown-original-dutch-processed-unsweetened-100-cocoa-beans', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,513 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,513 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,513 INFO sqlalchemy.engine.Engine [insertmanyvalues 76/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/products/van-houten-milk-chocolate-couverture-35-6-cocoa-1-kg', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,513 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 76/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/products/van-houten-milk-chocolate-couverture-35-6-cocoa-1-kg', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,513 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,513 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,513 INFO sqlalchemy.engine.Engine [insertmanyvalues 77/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-banana', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,513 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 77/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-banana', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,513 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,513 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,513 INFO sqlalchemy.engine.Engine [insertmanyvalues 78/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-cardamom', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,513 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 78/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-cardamom', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,513 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,513 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,513 INFO sqlalchemy.engine.Engine [insertmanyvalues 79/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/fab-oil-soluble-whisky', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,513 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 79/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/fab-oil-soluble-whisky', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,513 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,513 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,514 INFO sqlalchemy.engine.Engine [insertmanyvalues 80/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/all-purpose-flour/products/van-houten-milk-chocolate-couverture-35-6-cocoa-1-kg', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,514 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 80/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/all-purpose-flour/products/van-houten-milk-chocolate-couverture-35-6-cocoa-1-kg', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,514 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,514 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,514 INFO sqlalchemy.engine.Engine [insertmanyvalues 81/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/fab-oil-soluble-gulkand', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,514 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 81/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/fab-oil-soluble-gulkand', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,514 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,514 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,514 INFO sqlalchemy.engine.Engine [insertmanyvalues 82/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-lemon', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,514 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 82/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-lemon', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,514 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,514 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,514 INFO sqlalchemy.engine.Engine [insertmanyvalues 83/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-pineapple', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,514 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 83/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-pineapple', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,514 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,514 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,514 INFO sqlalchemy.engine.Engine [insertmanyvalues 84/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-rose', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,514 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 84/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-rose', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,514 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,514 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,514 INFO sqlalchemy.engine.Engine [insertmanyvalues 85/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/van-houten-white-chocolate-couverture-29-3-cocoa-1-kg', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,514 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 85/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/van-houten-white-chocolate-couverture-29-3-cocoa-1-kg', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,514 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,514 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,514 INFO sqlalchemy.engine.Engine [insertmanyvalues 86/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-mix-fruit', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,514 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 86/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-mix-fruit', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,514 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,514 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,514 INFO sqlalchemy.engine.Engine [insertmanyvalues 87/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-orange', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,514 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 87/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-orange', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,514 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,514 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,514 INFO sqlalchemy.engine.Engine [insertmanyvalues 88/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-cashew-nut', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,514 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 88/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-cashew-nut', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,514 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,514 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,514 INFO sqlalchemy.engine.Engine [insertmanyvalues 89/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-natural-flavours', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,514 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 89/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-natural-flavours', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,514 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,514 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,514 INFO sqlalchemy.engine.Engine [insertmanyvalues 90/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-vanilla', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,514 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 90/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-vanilla', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,514 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,514 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,514 INFO sqlalchemy.engine.Engine [insertmanyvalues 91/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/fab-oil-soluble-whisky', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,514 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 91/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/fab-oil-soluble-whisky', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,514 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,514 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,514 INFO sqlalchemy.engine.Engine [insertmanyvalues 92/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,514 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 92/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,515 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,515 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,515 INFO sqlalchemy.engine.Engine [insertmanyvalues 93/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-coffee', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,515 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 93/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-coffee', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,515 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,515 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,515 INFO sqlalchemy.engine.Engine [insertmanyvalues 94/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-coconut', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,515 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 94/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-coconut', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,515 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,515 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,515 INFO sqlalchemy.engine.Engine [insertmanyvalues 95/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/cart', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,515 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 95/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/cart', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,515 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,515 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,515 INFO sqlalchemy.engine.Engine [insertmanyvalues 96/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-butterscotch', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,515 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 96/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-butterscotch', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,515 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,515 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,515 INFO sqlalchemy.engine.Engine [insertmanyvalues 97/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/products/van-houten-white-chocolate-couverture-29-3-cocoa-1-kg', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,515 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 97/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/products/van-houten-white-chocolate-couverture-29-3-cocoa-1-kg', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,515 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,515 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,515 INFO sqlalchemy.engine.Engine [insertmanyvalues 98/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-cashew-nut', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,515 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 98/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-cashew-nut', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,515 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,515 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,515 INFO sqlalchemy.engine.Engine [insertmanyvalues 99/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/fab-oil-soluble-vodka', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,515 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 99/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/fab-oil-soluble-vodka', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,515 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,515 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,515 INFO sqlalchemy.engine.Engine [insertmanyvalues 100/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-banana', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,515 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 100/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-banana', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,515 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,515 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,515 INFO sqlalchemy.engine.Engine [insertmanyvalues 101/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/pages/contact', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,515 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 101/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/pages/contact', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,515 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,515 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,515 INFO sqlalchemy.engine.Engine [insertmanyvalues 102/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/all-purpose-flour/products/al-barakah-premium-arabian-date-powder-250gram-date-sugar-natural-sweetener-perfect-for-desserts-baking-and-smoothie-sugar-substitute', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,515 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 102/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/all-purpose-flour/products/al-barakah-premium-arabian-date-powder-250gram-date-sugar-natural-sweetener-perfect-for-desserts-baking-and-smoothie-sugar-substitute', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,515 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,515 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,515 INFO sqlalchemy.engine.Engine [insertmanyvalues 103/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-coconut', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,515 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 103/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-coconut', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,515 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,515 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,515 INFO sqlalchemy.engine.Engine [insertmanyvalues 104/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-passion-fruit', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,515 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 104/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-passion-fruit', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,515 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,515 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,516 INFO sqlalchemy.engine.Engine [insertmanyvalues 105/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/products/star-cookie-cutter-set-5-pcs-stainless-steel-five-pointed-star-biscuit-molds-fondant-cake-cookie-cutter-set-pastry-mold-for-3d-christmas-tree-linzer-cookies', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,516 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 105/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/products/star-cookie-cutter-set-5-pcs-stainless-steel-five-pointed-star-biscuit-molds-fondant-cake-cookie-cutter-set-pastry-mold-for-3d-christmas-tree-linzer-cookies', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,516 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,516 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,516 INFO sqlalchemy.engine.Engine [insertmanyvalues 106/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/silicone-mould', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,516 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 106/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/silicone-mould', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,516 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,516 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,516 INFO sqlalchemy.engine.Engine [insertmanyvalues 107/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/account/login', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,516 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 107/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/account/login', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,516 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,516 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,516 INFO sqlalchemy.engine.Engine [insertmanyvalues 108/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-caramel', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,516 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 108/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-caramel', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,516 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,516 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,516 INFO sqlalchemy.engine.Engine [insertmanyvalues 109/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/101-piping-nozzle', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,516 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 109/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/101-piping-nozzle', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,516 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,516 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,516 INFO sqlalchemy.engine.Engine [insertmanyvalues 110/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-mint-2', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,516 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 110/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-mint-2', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,516 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,516 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,516 INFO sqlalchemy.engine.Engine [insertmanyvalues 111/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/products/van-houten-dark-chocolate-couverture-46-5-cocoa-1-kg', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,516 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 111/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/products/van-houten-dark-chocolate-couverture-46-5-cocoa-1-kg', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,516 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,516 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,516 INFO sqlalchemy.engine.Engine [insertmanyvalues 112/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-irish-cream-1', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,516 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 112/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-irish-cream-1', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,516 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,516 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,516 INFO sqlalchemy.engine.Engine [insertmanyvalues 113/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-caramel', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,516 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 113/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-caramel', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,516 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,516 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,516 INFO sqlalchemy.engine.Engine [insertmanyvalues 114/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-lemon', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,516 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 114/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-lemon', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,516 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,516 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,516 INFO sqlalchemy.engine.Engine [insertmanyvalues 115/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/products/lady-finger-tiramisu-400gms', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,516 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 115/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/products/lady-finger-tiramisu-400gms', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,516 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,516 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,516 INFO sqlalchemy.engine.Engine [insertmanyvalues 116/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/all-purpose-flour/products/van-houten-dark-chocolate-couverture-46-5-cocoa-1-kg', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,516 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 116/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/all-purpose-flour/products/van-houten-dark-chocolate-couverture-46-5-cocoa-1-kg', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,516 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,516 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,516 INFO sqlalchemy.engine.Engine [insertmanyvalues 117/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-paan', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,516 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 117/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-paan', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,517 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,517 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,517 INFO sqlalchemy.engine.Engine [insertmanyvalues 118/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-almond', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,517 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 118/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-almond', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,517 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,517 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,517 INFO sqlalchemy.engine.Engine [insertmanyvalues 119/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/decorative-items', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,517 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 119/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/decorative-items', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,517 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,517 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,517 INFO sqlalchemy.engine.Engine [insertmanyvalues 120/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/wpm@a7f653a7w82d20f99pe720974fm204fcef1/custom/web-pixel-shopify-custom-pixel@0420/sandbox/modern', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,517 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 120/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/wpm@a7f653a7w82d20f99pe720974fm204fcef1/custom/web-pixel-shopify-custom-pixel@0420/sandbox/modern', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,517 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,517 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,517 INFO sqlalchemy.engine.Engine [insertmanyvalues 121/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-butterscotch', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,517 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 121/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-butterscotch', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,517 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,517 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,517 INFO sqlalchemy.engine.Engine [insertmanyvalues 122/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/products/orange-belgium', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,517 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 122/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/products/orange-belgium', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,517 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,517 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,517 INFO sqlalchemy.engine.Engine [insertmanyvalues 123/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/products/christmas-tree-cookie-cutter-set-3-pieces-aluminium-xmas-tree-cookie-cutters-with-recipe-booklet-biscuit-molds-for-chirstmas-day-party-decorations-diy-gift-family-kids-baking-tools', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,517 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 123/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/products/christmas-tree-cookie-cutter-set-3-pieces-aluminium-xmas-tree-cookie-cutters-with-recipe-booklet-biscuit-molds-for-chirstmas-day-party-decorations-diy-gift-family-kids-baking-tools', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,517 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,517 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,517 INFO sqlalchemy.engine.Engine [insertmanyvalues 124/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/apyura-premium-spread/products/cookie-and-crunch', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,517 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 124/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/apyura-premium-spread/products/cookie-and-crunch', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,517 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,517 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,517 INFO sqlalchemy.engine.Engine [insertmanyvalues 125/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/orange-belgium', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,517 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 125/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/orange-belgium', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,517 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,517 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,517 INFO sqlalchemy.engine.Engine [insertmanyvalues 126/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble-premium', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,517 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 126/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble-premium', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,517 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,517 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,517 INFO sqlalchemy.engine.Engine [insertmanyvalues 127/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-chocolate', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,517 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 127/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-chocolate', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,517 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,517 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,517 INFO sqlalchemy.engine.Engine [insertmanyvalues 128/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/fab-oil-soluble-gulkand', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,517 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 128/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/fab-oil-soluble-gulkand', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,517 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,517 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,518 INFO sqlalchemy.engine.Engine [insertmanyvalues 129/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-coffee', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,518 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 129/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-coffee', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,518 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,518 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,518 INFO sqlalchemy.engine.Engine [insertmanyvalues 130/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/fab-oil-soluble-saffron', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,518 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 130/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/fab-oil-soluble-saffron', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,518 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,518 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,518 INFO sqlalchemy.engine.Engine [insertmanyvalues 131/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-chocolate', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,518 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 131/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-chocolate', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,518 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,518 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,518 INFO sqlalchemy.engine.Engine [insertmanyvalues 132/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-almond', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,518 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 132/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-almond', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,518 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,518 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,518 INFO sqlalchemy.engine.Engine [insertmanyvalues 133/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-kewra', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,518 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 133/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-kewra', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,518 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,518 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,518 INFO sqlalchemy.engine.Engine [insertmanyvalues 134/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/apyura-center-filling-create-new-view', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,518 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 134/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/apyura-center-filling-create-new-view', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,518 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,518 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,518 INFO sqlalchemy.engine.Engine [insertmanyvalues 135/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-litchi', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,518 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 135/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/oil-soluble-litchi', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,518 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,518 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,518 INFO sqlalchemy.engine.Engine [insertmanyvalues 136/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/pages/wishlist', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,518 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 136/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/pages/wishlist', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,518 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,518 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,518 INFO sqlalchemy.engine.Engine [insertmanyvalues 137/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-blueberry', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,518 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 137/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-blueberry', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,519 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,519 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,519 INFO sqlalchemy.engine.Engine [insertmanyvalues 138/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-premium-water-soluble', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,519 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 138/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/fab-premium-water-soluble', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,519 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,519 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,519 INFO sqlalchemy.engine.Engine [insertmanyvalues 139/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/all', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,519 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 139/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/collections/all', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,519 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,519 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,519 INFO sqlalchemy.engine.Engine [insertmanyvalues 140/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'http://bakingcart.com/cdn/shop/t/2/assets/logo.png?v=116277239867421797551702904462', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,519 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 140/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'http://bakingcart.com/cdn/shop/t/2/assets/logo.png?v=116277239867421797551702904462', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,519 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,519 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,519 INFO sqlalchemy.engine.Engine [insertmanyvalues 141/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/van-houten-milk-chocolate-couverture-35-6-cocoa-1-kg', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,519 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 141/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/van-houten-milk-chocolate-couverture-35-6-cocoa-1-kg', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,519 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,519 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:52:05,519 INFO sqlalchemy.engine.Engine [insertmanyvalues 142/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/al-barakah-premium-arabian-date-powder-250gram-date-sugar-natural-sweetener-perfect-for-desserts-baking-and-smoothie-sugar-substitute', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,519 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 142/142 (ordered; batch not supported)] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 'https://bakingcart.com/al-barakah-premium-arabian-date-powder-250gram-date-sugar-natural-sweetener-perfect-for-desserts-baking-and-smoothie-sugar-substitute', 1, '', '', 0, '', '', '', '', 'default')
2025-07-28 00:52:05,520 INFO sqlalchemy.engine.Engine COMMIT
2025-07-28 00:52:05,520 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-28 00:52:05,523 - app.utils.website_url_processor - INFO - Successfully stored 142 URLs for scrape_request_ref_id: b07e8022-c849-4042-a353-c99216bbasdfasdf5d74
2025-07-28 00:52:05,524 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-28 00:52:05,524 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 00:52:05,524 INFO sqlalchemy.engine.Engine INSERT INTO mcc_analysis_gemini (website, scrape_request_ref_id, result_status, mcc_code, business_category, business_description, reasoning, created_at, started_at, completed_at, failed_at, last_updated, error_message, details, org_id, processing_status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-28 00:52:05,524 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_analysis_gemini (website, scrape_request_ref_id, result_status, mcc_code, business_category, business_description, reasoning, created_at, started_at, completed_at, failed_at, last_updated, error_message, details, org_id, processing_status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-28 00:52:05,524 INFO sqlalchemy.engine.Engine [cached since 129.8s ago] ('https://bakingcart.com/', 'b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', None, None, None, None, None, '2025-07-28T00:52:05.523893Z', None, None, None, None, None, None, 'default', 'PENDING')
2025-07-28 00:52:05,524 - sqlalchemy.engine.Engine - INFO - [cached since 129.8s ago] ('https://bakingcart.com/', 'b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', None, None, None, None, None, '2025-07-28T00:52:05.523893Z', None, None, None, None, None, None, 'default', 'PENDING')
2025-07-28 00:52:05,524 INFO sqlalchemy.engine.Engine COMMIT
2025-07-28 00:52:05,524 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-28 00:52:05,526 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-28 00:52:05,526 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 00:52:05,527 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.id = ?
2025-07-28 00:52:05,527 - sqlalchemy.engine.Engine - INFO - SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.id = ?
2025-07-28 00:52:05,527 INFO sqlalchemy.engine.Engine [cached since 129.8s ago] (26,)
2025-07-28 00:52:05,527 - sqlalchemy.engine.Engine - INFO - [cached since 129.8s ago] (26,)
2025-07-28 00:52:05,527 - app.routers.mcc_analysis - INFO - Created new MCC analysis with ID 26
2025-07-28 00:52:05,527 - app.routers.mcc_analysis - INFO - Background task queued for analysis 26
2025-07-28 00:52:05,528 INFO sqlalchemy.engine.Engine ROLLBACK
2025-07-28 00:52:05,528 - sqlalchemy.engine.Engine - INFO - ROLLBACK
[2025-07-28 00:52:05][request_middleware][NO_REF] INFO: Request completed: POST /mcc-analysis/ - 200
{
  "request_id": "d2015e95-9460-4ca6-829c-1c7863d46508",
  "status_code": 200,
  "response_time_ms": 37.5
}
INFO:     127.0.0.1:45918 - "POST /mcc-analysis/ HTTP/1.1" 200 OK
2025-07-28 00:52:05,529 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-28 00:52:05,529 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 00:52:05,529 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id AS mcc_analysis_gemini_id, mcc_analysis_gemini.website AS mcc_analysis_gemini_website, mcc_analysis_gemini.scrape_request_ref_id AS mcc_analysis_gemini_scrape_request_ref_id, mcc_analysis_gemini.result_status AS mcc_analysis_gemini_result_status, mcc_analysis_gemini.mcc_code AS mcc_analysis_gemini_mcc_code, mcc_analysis_gemini.business_category AS mcc_analysis_gemini_business_category, mcc_analysis_gemini.business_description AS mcc_analysis_gemini_business_description, mcc_analysis_gemini.reasoning AS mcc_analysis_gemini_reasoning, mcc_analysis_gemini.created_at AS mcc_analysis_gemini_created_at, mcc_analysis_gemini.started_at AS mcc_analysis_gemini_started_at, mcc_analysis_gemini.completed_at AS mcc_analysis_gemini_completed_at, mcc_analysis_gemini.failed_at AS mcc_analysis_gemini_failed_at, mcc_analysis_gemini.last_updated AS mcc_analysis_gemini_last_updated, mcc_analysis_gemini.error_message AS mcc_analysis_gemini_error_message, mcc_analysis_gemini.details AS mcc_analysis_gemini_details, mcc_analysis_gemini.org_id AS mcc_analysis_gemini_org_id, mcc_analysis_gemini.processing_status AS mcc_analysis_gemini_processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.id = ?
2025-07-28 00:52:05,529 - sqlalchemy.engine.Engine - INFO - SELECT mcc_analysis_gemini.id AS mcc_analysis_gemini_id, mcc_analysis_gemini.website AS mcc_analysis_gemini_website, mcc_analysis_gemini.scrape_request_ref_id AS mcc_analysis_gemini_scrape_request_ref_id, mcc_analysis_gemini.result_status AS mcc_analysis_gemini_result_status, mcc_analysis_gemini.mcc_code AS mcc_analysis_gemini_mcc_code, mcc_analysis_gemini.business_category AS mcc_analysis_gemini_business_category, mcc_analysis_gemini.business_description AS mcc_analysis_gemini_business_description, mcc_analysis_gemini.reasoning AS mcc_analysis_gemini_reasoning, mcc_analysis_gemini.created_at AS mcc_analysis_gemini_created_at, mcc_analysis_gemini.started_at AS mcc_analysis_gemini_started_at, mcc_analysis_gemini.completed_at AS mcc_analysis_gemini_completed_at, mcc_analysis_gemini.failed_at AS mcc_analysis_gemini_failed_at, mcc_analysis_gemini.last_updated AS mcc_analysis_gemini_last_updated, mcc_analysis_gemini.error_message AS mcc_analysis_gemini_error_message, mcc_analysis_gemini.details AS mcc_analysis_gemini_details, mcc_analysis_gemini.org_id AS mcc_analysis_gemini_org_id, mcc_analysis_gemini.processing_status AS mcc_analysis_gemini_processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.id = ?
2025-07-28 00:52:05,529 INFO sqlalchemy.engine.Engine [cached since 128.5s ago] (26,)
2025-07-28 00:52:05,529 - sqlalchemy.engine.Engine - INFO - [cached since 128.5s ago] (26,)
2025-07-28 00:52:05,529 INFO sqlalchemy.engine.Engine UPDATE mcc_analysis_gemini SET started_at=?, processing_status=? WHERE mcc_analysis_gemini.id = ?
2025-07-28 00:52:05,529 - sqlalchemy.engine.Engine - INFO - UPDATE mcc_analysis_gemini SET started_at=?, processing_status=? WHERE mcc_analysis_gemini.id = ?
2025-07-28 00:52:05,529 INFO sqlalchemy.engine.Engine [cached since 128.5s ago] ('2025-07-28T00:52:05.529651Z', 'PROCESSING', 26)
2025-07-28 00:52:05,529 - sqlalchemy.engine.Engine - INFO - [cached since 128.5s ago] ('2025-07-28T00:52:05.529651Z', 'PROCESSING', 26)
2025-07-28 00:52:05,530 INFO sqlalchemy.engine.Engine COMMIT
2025-07-28 00:52:05,530 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-28 00:52:05,532 - app.routers.mcc_analysis - INFO - Updated MccAnalysis status to PROCESSING
[2025-07-28 00:52:05][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Starting MCC analysis process
{
  "scrape_request_ref_id": "b07e8022-c849-4042-a353-c99216bbasdfasdf5d74",
  "org_id": "default"
}
[2025-07-28 00:52:05][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Step 1: Getting URLs data
[2025-07-28 00:52:05][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Checking if URLs exist in database
{
  "scrape_request_ref_id": "b07e8022-c849-4042-a353-c99216bbasdfasdf5d74"
}
2025-07-28 00:52:05,532 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-28 00:52:05,532 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 00:52:05,532 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-28 00:52:05,532 - sqlalchemy.engine.Engine - INFO - SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-28 00:52:05,532 INFO sqlalchemy.engine.Engine [cached since 129.9s ago] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74',)
2025-07-28 00:52:05,532 - sqlalchemy.engine.Engine - INFO - [cached since 129.9s ago] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74',)
2025-07-28 00:52:05,534 - app.utils.website_url_processor - INFO - Retrieved 142 URLs for scrape_request_ref_id: b07e8022-c849-4042-a353-c99216bbasdfasdf5d74
2025-07-28 00:52:05,535 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = ?
2025-07-28 00:52:05,535 - sqlalchemy.engine.Engine - INFO - SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = ?
2025-07-28 00:52:05,535 INFO sqlalchemy.engine.Engine [cached since 129.9s ago] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74',)
2025-07-28 00:52:05,535 - sqlalchemy.engine.Engine - INFO - [cached since 129.9s ago] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74',)
[2025-07-28 00:52:05][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: URLs found in database
{
  "website": "https://bakingcart.com/",
  "total_depths": 1,
  "total_urls": 142
}
[2025-07-28 00:52:05][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Successfully unpacked URL check result
{
  "urls_exist": true,
  "urls_data_type": "<class 'dict'>"
}
[2025-07-28 00:52:05][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Total parsed URLs found: 142
[2025-07-28 00:52:05][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Step 2: Preparing data for analysis
[2025-07-28 00:52:05][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Preparing data for MCC analysis
{
  "website": "https://bakingcart.com/"
}
[2025-07-28 00:52:05][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Got URLs by depth
{
  "depth_1_count": 142,
  "depth_2_count": 0,
  "scrape_request_ref_id": "b07e8022-c849-4042-a353-c99216bbasdfasdf5d74"
}
[2025-07-28 00:52:05][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Checking for existing classification results in database
2025-07-28 00:52:05,535 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-28 00:52:05,535 - sqlalchemy.engine.Engine - INFO - SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-28 00:52:05,535 INFO sqlalchemy.engine.Engine [cached since 129.9s ago] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74',)
2025-07-28 00:52:05,535 - sqlalchemy.engine.Engine - INFO - [cached since 129.9s ago] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74',)
2025-07-28 00:52:08,041 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
[2025-07-28 00:52:05][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Found 142 stored URL records
[2025-07-28 00:52:05][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Stored classification analysis
{
  "total_stored_urls": 142,
  "unreachable_count": 142,
  "classified_count": 0,
  "stored_categories": {
    "urls_not_reachable": 142
  }
}
[2025-07-28 00:52:05][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] WARNING: [RACE CONDITION FIX] All stored URLs marked unreachable - forcing fresh classification
{
  "total_urls": 142,
  "unreachable_urls": 142,
  "classified_urls": 0,
  "action": "FORCING_FRESH_CLASSIFICATION",
  "scrape_ref": "b07e8022-c849-4042-a353-c99216bbasdfasdf5d74",
  "fix_type": "RACE_CONDITION_PROTECTION",
  "time": "2025-07-28T00:52:05.536960"
}
[2025-07-28 00:52:05][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: No usable stored classification results found, proceeding with fresh API calls
[2025-07-28 00:52:05][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Starting soft classification of URLs
[2025-07-28 00:52:05][url_classification_b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Starting soft classification
{
  "website": "https://bakingcart.com/",
  "urls_depth_1_count": 142,
  "urls_depth_2_count": 0
}
[2025-07-28 00:52:05][url_classification_b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Dictionary for soft classification prepared
{
  "url_count": 142,
  "sample_urls": [
    "https://bakingcart.com/",
    "https://bakingcart.com/oil-soluble-passion-fruit",
    "https://bakingcart.com/collections/apyura-premium-spread/products/orange-belgium"
  ]
}
[2025-07-28 00:52:05][test-analysis][NO_REF] INFO: Starting URL processing for model policy result
{
  "website": "https://bakingcart.com/",
  "total_urls": 142
}
[2025-07-28 00:52:05][test-analysis][NO_REF] INFO: Token calculation: system=265, base_user=988, available_for_urls=87747
[2025-07-28 00:52:05][test-analysis][NO_REF] INFO: Total URL tokens: 2937, Available: 87747
[2025-07-28 00:52:05][test-analysis][NO_REF] INFO: All URLs fit within token limit
[2025-07-28 00:52:05][test-analysis][NO_REF] INFO: Final URLs after token limiting
{
  "original_url_count": 142,
  "final_url_count": 142,
  "urls_trimmed": 0,
  "system_tokens": 265,
  "base_user_tokens": 988,
  "url_tokens": 2937,
  "final_total_tokens": 4190,
  "token_limit": 90000,
  "remaining_tokens": 85810
}
[2025-07-28 00:52:05][test-analysis][NO_REF] INFO: Final prompt verification
{
  "actual_prompt_tokens": 4897,
  "token_limit": 90000,
  "within_limit": true,
  "processing_time": 0.004441738128662109
}
[2025-07-28 00:52:05][url_classification_b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Sending request to OpenAI API using model gpt-4o...
[2025-07-28 00:52:05][url_classification_b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Processing URLs for classification. This may take some time...
[2025-07-28 00:52:08][url_classification_b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Usage: CompletionUsage(completion_tokens=140, prompt_tokens=4773, total_tokens=4913, completion_tokens_details=CompletionTokensDetails(accepted_prediction_tokens=0, audio_tokens=0, reasoning_tokens=0, rejected_prediction_tokens=0), prompt_tokens_details=PromptTokensDetails(audio_tokens=0, cached_tokens=0))
[2025-07-28 00:52:09][url_classification_b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Soft classification response received
{
  "response_length": 457,
  "response_preview": "```json\n{\n    \"home_page\": [0],\n    \"about_us\": [4],\n    \"terms_and_condition\": [],\n    \"returns_cancellation_exchange\": [],\n    \"privacy_policy\": [],\n    \"shipping_delivery\": [],\n    \"contact_us\": [1..."
}
[2025-07-28 00:52:09][url_classification_b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Preparing data for model policy result
{
  "website": "https://bakingcart.com/",
  "dictionary_1_count": 142,
  "total_chars": 9898,
  "urls_sample": [
    "https://bakingcart.com/",
    "https://bakingcart.com/oil-soluble-passion-fruit",
    "https://bakingcart.com/collections/apyura-premium-spread/products/orange-belgium"
  ]
}
[2025-07-28 00:52:09][url_classification_b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Got policy URLs classification
{
  "policy_urls": {
    "home_page": [
      0
    ],
    "about_us": [
      4
    ],
    "terms_and_condition": [],
    "returns_cancellation_exchange": [],
    "privacy_policy": [],
    "shipping_delivery": [],
    "contact_us": [
      100
    ],
    "products": [
      1,
      5,
      11,
      16,
      23
    ],
    "services": [
      12,
      15,
      17,
      29,
      52
    ],
    "catalogue": [
      138
    ],
    "instagram_page": [],
    "facebook_page": [],
    "twitter_page": [],
    "linkedin_page": [],
    "youtube_page": [],
    "pinterest_page": []
  },
  "categories_found": [
    "home_page",
    "about_us",
    "terms_and_condition",
    "returns_cancellation_exchange",
    "privacy_policy",
    "shipping_delivery",
    "contact_us",
    "products",
    "services",
    "catalogue",
    "instagram_page",
    "facebook_page",
    "twitter_page",
    "linkedin_page",
    "youtube_page",
    "pinterest_page"
  ]
}
[2025-07-28 00:52:09][url_classification_b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Completed soft classification
{
  "mcc_dict": {
    "home_page": [
      "https://bakingcart.com/"
    ],
    "about_us": [
      "https://bakingcart.com/pages/about-us"
    ],
    "terms_and_condition": [],
    "returns_cancellation_exchange": [],
    "privacy_policy": [],
    "shipping_delivery": [],
    "contact_us": [
      "https://bakingcart.com/pages/contact"
    ],
    "products": [
      "https://bakingcart.com/oil-soluble-passion-fruit",
      "https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-rose",
      "https://bakingcart.com/products/titamisu",
      "https://bakingcart.com/products/al-barakah-premium-arabian-date-powder-250gram-date-sugar-natural-sweetener-perfect-for-desserts-baking-and-smoothie-sugar-substitute",
      "https://bakingcart.com/products/101-piping-nozzle"
    ],
    "services": [
      "https://bakingcart.com/collections/all-purpose-flour",
      "https://bakingcart.com/collections",
      "https://bakingcart.com/collections/apyura-premium-spread",
      "https://bakingcart.com/collections/all-collections",
      "https://bakingcart.com/collections/fab-natural-extracts"
    ],
    "catalogue": [
      "https://bakingcart.com/collections/all"
    ],
    "instagram_page": [],
    "facebook_page": [],
    "twitter_page": [],
    "linkedin_page": [],
    "youtube_page": [],
    "pinterest_page": []
  },
  "total_classified_urls": 14,
  "priority_urls_count": 9
}
[2025-07-28 00:52:09][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Successfully unpacked soft classification result
{
  "output_df_type": "<class 'pandas.core.frame.DataFrame'>",
  "soft_classified_urls_type": "<class 'dict'>"
}
[2025-07-28 00:52:09][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Soft classification unreachable URL analysis
{
  "total_urls": 142,
  "unreachable_count": 0,
  "unreachable_ratio": "0/142",
  "unreachable_percentage": "0.0%",
  "urls_by_category": {
    "home_page": 1,
    "about_us": 1,
    "terms_and_condition": 0,
    "returns_cancellation_exchange": 0,
    "privacy_policy": 0,
    "shipping_delivery": 0,
    "contact_us": 1,
    "products": 5,
    "services": 5,
    "catalogue": 1,
    "instagram_page": 0,
    "facebook_page": 0,
    "twitter_page": 0,
    "linkedin_page": 0,
    "youtube_page": 0,
    "pinterest_page": 0
  }
}
[2025-07-28 00:52:09][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Soft classification completed
{
  "categories_found": [
    "home_page",
    "about_us",
    "terms_and_condition",
    "returns_cancellation_exchange",
    "privacy_policy",
    "shipping_delivery",
    "contact_us",
    "products",
    "services",
    "catalogue",
    "instagram_page",
    "facebook_page",
    "twitter_page",
    "linkedin_page",
    "youtube_page",
    "pinterest_page"
  ]
}2025-07-28 00:52:12,085 - google_genai.models - INFO - AFC is enabled with max remote calls: 20000.
2025-07-28 00:52:28,869 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent "HTTP/1.1 200 OK"
2025-07-28 00:52:28,875 - google_genai.models - INFO - AFC remote call 1 is done.

[2025-07-28 00:52:09][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Starting hard classification of URLs
[2025-07-28 00:52:09][url_classification_b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Starting hard classification verification for POLICY URLs only
{
  "website": "https://bakingcart.com/"
}
[2025-07-28 00:52:09][url_classification_b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Including policy category 'home_page' in hard classification
{
  "url_count": 1
}
[2025-07-28 00:52:09][url_classification_b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Including policy category 'about_us' in hard classification
{
  "url_count": 1
}
[2025-07-28 00:52:09][url_classification_b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Including policy category 'terms_and_condition' in hard classification
{
  "url_count": 0
}
[2025-07-28 00:52:09][url_classification_b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Including policy category 'returns_cancellation_exchange' in hard classification
{
  "url_count": 0
}
[2025-07-28 00:52:09][url_classification_b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Including policy category 'privacy_policy' in hard classification
{
  "url_count": 0
}
[2025-07-28 00:52:09][url_classification_b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Including policy category 'shipping_delivery' in hard classification
{
  "url_count": 0
}
[2025-07-28 00:52:09][url_classification_b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Including policy category 'contact_us' in hard classification
{
  "url_count": 1
}
[2025-07-28 00:52:09][url_classification_b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Including other category 'products' in hard classification
{
  "url_count": 5
}
[2025-07-28 00:52:09][url_classification_b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Including other category 'services' in hard classification
{
  "url_count": 5
}
[2025-07-28 00:52:09][url_classification_b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Including policy category 'catalogue' in hard classification
{
  "url_count": 1
}
[2025-07-28 00:52:09][url_classification_b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Skipping social media category 'instagram_page' from hard classification
{
  "url_count": 0,
  "reason": "social_media_uses_soft_classification_only"
}
[2025-07-28 00:52:09][url_classification_b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Skipping social media category 'facebook_page' from hard classification
{
  "url_count": 0,
  "reason": "social_media_uses_soft_classification_only"
}
[2025-07-28 00:52:09][url_classification_b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Skipping social media category 'twitter_page' from hard classification
{
  "url_count": 0,
  "reason": "social_media_uses_soft_classification_only"
}
[2025-07-28 00:52:09][url_classification_b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Skipping social media category 'linkedin_page' from hard classification
{
  "url_count": 0,
  "reason": "social_media_uses_soft_classification_only"
}
[2025-07-28 00:52:09][url_classification_b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Skipping social media category 'youtube_page' from hard classification
{
  "url_count": 0,
  "reason": "social_media_uses_soft_classification_only"
}
[2025-07-28 00:52:09][url_classification_b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Skipping social media category 'pinterest_page' from hard classification
{
  "url_count": 0,
  "reason": "social_media_uses_soft_classification_only"
}
[2025-07-28 00:52:09][test-analysis][NO_REF] INFO: Hard classification input
{
  "total_urls": 14,
  "website": "https://bakingcart.com/",
  "url_limit": 20
}
[2025-07-28 00:52:09][test-analysis][NO_REF] INFO: All URLs fit within hard classification limit
{
  "total_urls": 14,
  "limit": 20
}
[2025-07-28 00:52:09][gemini_optimizer_post_soft_classification][NO_REF] INFO: Optimizing Gemini call for task_type: post_soft_classification
{
  "received_task_type": "post_soft_classification"
}
[2025-07-28 00:52:12][post_soft_classification_unknown][NO_REF] INFO: Starting Gemini API call
{
  "model": "gemini-2.5-flash",
  "timeout": 120,
  "max_retries": 3,
  "prompt_length": 6045,
  "context": {
    "task_type": "post_soft_classification"
  }
}
[2025-07-28 00:52:12][post_soft_classification_unknown][NO_REF] INFO: Gemini API attempt 1/3
[2025-07-28 00:52:17][text_extraction][NO_REF] ERROR: Failed to navigate to URL with proxy for text extraction (timeout)
{
  "error": "{'error': {'error': 'Page.goto: Timeout 36000ms exceeded.\\nCall log:\\n  - navigating to \"https://www.epaal.in/pages/about-us\", waiting until \"load\"\\n', 'url': 'https://www.epaal.in/pages/about-us', 'retry': 0}}",
  "error_type": "dict"
}
Traceback for analysis text_extraction:
Error printing traceback: 'dict' object has no attribute '__traceback__'
[2025-07-28 00:52:17][text_extraction][NO_REF] ERROR: Failed to access https://www.epaal.in/pages/about-us for text extraction with both direct and proxy connections
[2025-07-28 00:52:17][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] WARNING: ⚠️ Playwright extraction insufficient for about_us, trying requests method
[2025-07-28 00:52:18][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: ✅ Text extracted from about_us: 5000 characters
[2025-07-28 00:52:18][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: 📄 Extracting text from catalogue: https://www.epaal.in/products/spiral-triple-color-2-pcs
[2025-07-28 00:52:18][text_extraction][NO_REF] INFO: Starting text extraction
{
  "url": "https://www.epaal.in/products/spiral-triple-color-2-pcs",
  "timeout": 60
}
[2025-07-28 00:52:28][post_soft_classification_unknown][NO_REF] INFO: Gemini API Usage: cache_tokens_details=None cached_content_token_count=None candidates_token_count=493 candidates_tokens_details=None prompt_token_count=1514 prompt_tokens_details=[ModalityTokenCount(
  modality=<MediaModality.TEXT: 'TEXT'>,
  token_count=1514
)] thoughts_token_count=1470 tool_use_prompt_token_count=None tool_use_prompt_tokens_details=None total_token_count=3477 traffic_type=None
[2025-07-28 00:52:28][post_soft_classification_unknown][NO_REF] INFO: Gemini API call successful
{
  "attempt": 1,
  "response_length": 326,
  "finish_reason": "STOP"
}
[2025-07-28 00:52:29][url_classification_b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Gemini response received for hard classification
{
  "response_length": 326,
  "response_preview": "```json\n{\n    \"home_page\": [0],\n    \"about_us\": [5],\n    \"terms_and_condition\": [],\n    \"returns_cancellation_exchange\": [],\n    \"privacy_policy\": [],\n    \"shipping_delivery\": [],\n    \"contact_us\": [3..."
}
[2025-07-28 00:52:29][url_classification_b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Parsing hard classification response
{
  "response": "```json\n{\n    \"home_page\": [0],\n    \"about_us\": [5],\n    \"terms_and_condition\": [],\n    \"returns_cancellation_exchange\": [],\n    \"privacy_policy\": [],\n    \"shipping_delivery\": [],\n    \"contact_us\": [3],\n    \"catalogue\": [0, 1, 4, 6, 7, 8, 9, 11, 13],\n    \"urls_not_reachable\": [],\n    \"Unreachable_via_tool\": [2, 10, 12]\n}\n```"
}
[2025-07-28 00:52:29][url_classification_b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Verified indices dictionary created
{
  "indices_categories": [
    "home_page",
    "about_us",
    "terms_and_condition",
    "returns_cancellation_exchange",
    "privacy_policy",
    "shipping_delivery",
    "contact_us",
    "catalogue",
    "urls_not_reachable",
    "Unreachable_via_tool"
  ],
  "total_indices": 15
}
[2025-07-28 00:52:29][url_classification_b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Hard classification reachability results
{
  "unreachable_urls_count": 3,
  "reachable_urls_count": 12,
  "unreachable_urls": [
    2,
    10,
    12
  ],
  "website": "https://bakingcart.com/",
  "backup_flow_trigger": true
}
[2025-07-28 00:52:29][url_classification_b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Verified indices dictionary created
{
  "indices_categories": [
    "home_page",
    "about_us",
    "terms_and_condition",
    "returns_cancellation_exchange",
    "privacy_policy",
    "shipping_delivery",
    "contact_us",
    "catalogue",
    "urls_not_reachable",
    "Unreachable_via_tool"
  ],
  "total_indices": 15
}
[2025-07-28 00:52:29][url_classification_b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Verified URLs dictionary created with social media merged
{
  "verified_categories": [
    "home_page",
    "about_us",
    "terms_and_condition",
    "returns_cancellation_exchange",
    "privacy_policy",
    "shipping_delivery",
    "contact_us",
    "catalogue",
    "urls_not_reachable",
    "Unreachable_via_tool"
  ],
  "category_counts": {
    "home_page": 1,
    "about_us": 1,
    "terms_and_condition": 0,
    "returns_cancellation_exchange": 0,
    "privacy_policy": 0,
    "shipping_delivery": 0,
    "contact_us": 1,
    "catalogue": 9,
    "urls_not_reachable": 0,
    "Unreachable_via_tool": 3
  },
  "total_verified_urls": 15,
  "social_media_merged": true
}
[2025-07-28 00:52:29][url_classification_b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Completed hard classification verification with social media merge
{
  "verified_categories": [
    "home_page",
    "about_us",
    "terms_and_condition",
    "returns_cancellation_exchange",
    "privacy_policy",
    "shipping_delivery",
    "contact_us",
    "catalogue",
    "urls_not_reachable",
    "Unreachable_via_tool"
  ],
  "total_verified_urls": 15,
  "policy_hard_classified": true,
  "social_media_soft_classified": true
}
[2025-07-28 00:52:29][url_classification_b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Saving hard classification results to database
2025-07-28 00:52:29,878 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-28 00:52:29,878 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 00:52:29,878 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-28 00:52:29,878 - sqlalchemy.engine.Engine - INFO - SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-28 00:52:29,878 INFO sqlalchemy.engine.Engine [cached since 154.2s ago] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74',)
2025-07-28 00:52:29,878 - sqlalchemy.engine.Engine - INFO - [cached since 154.2s ago] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74',)
[2025-07-28 00:52:29][url_classification_b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Retrieved 142 URL records for hard classification update
2025-07-28 00:52:29,881 INFO sqlalchemy.engine.Engine UPDATE website_urls_gemini SET hard_class=? WHERE website_urls_gemini.id = ?
2025-07-28 00:52:29,881 - sqlalchemy.engine.Engine - INFO - UPDATE website_urls_gemini SET hard_class=? WHERE website_urls_gemini.id = ?
2025-07-28 00:52:29,881 INFO sqlalchemy.engine.Engine [cached since 124.8s ago] [('["home_page", "catalogue"]', 1949), ('["Unreachable_via_tool"]', 1950), ('["about_us"]', 1953), ('["Unreachable_via_tool"]', 1954), ('["catalogue"]', 1960), ('["catalogue"]', 1961), ('["catalogue"]', 1964), ('["Unreachable_via_tool"]', 1965)  ... displaying 10 of 14 total bound parameter sets ...  ('["contact_us"]', 2049), ('["catalogue"]', 2087)]
2025-07-28 00:52:29,881 - sqlalchemy.engine.Engine - INFO - [cached since 124.8s ago] [('["home_page", "catalogue"]', 1949), ('["Unreachable_via_tool"]', 1950), ('["about_us"]', 1953), ('["Unreachable_via_tool"]', 1954), ('["catalogue"]', 1960), ('["catalogue"]', 1961), ('["catalogue"]', 1964), ('["Unreachable_via_tool"]', 1965)  ... displaying 10 of 14 total bound parameter sets ...  ('["contact_us"]', 2049), ('["catalogue"]', 2087)]
2025-07-28 00:52:29,882 INFO sqlalchemy.engine.Engine COMMIT
2025-07-28 00:52:29,882 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-07-28 00:52:29][url_classification_b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Updated 15 database records with hard classification
[2025-07-28 00:52:29][url_classification_b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Hard classification summary: 15 total URLs across 10 categories
[2025-07-28 00:52:29][url_classification_b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Hard classification category breakdown: {'home_page': 1, 'about_us': 1, 'terms_and_condition': 0, 'returns_cancellation_exchange': 0, 'privacy_policy': 0, 'shipping_delivery': 0, 'contact_us': 1, 'catalogue': 9, 'urls_not_reachable': 0, 'Unreachable_via_tool': 3}
[2025-07-28 00:52:29][url_classification_b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Successfully saved hard classification results to database
[2025-07-28 00:52:29][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Hard classification completed
{
  "categories_found": [
    "home_page",
    "about_us",
    "terms_and_condition",
    "returns_cancellation_exchange",
    "privacy_policy",
    "shipping_delivery",
    "contact_us",
    "catalogue",
    "urls_not_reachable",
    "Unreachable_via_tool"
  ]
}
[2025-07-28 00:52:29][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Hard classification results - detailed breakdown
{
  "category_counts": {
    "home_page": 1,
    "about_us": 1,
    "terms_and_condition": 0,
    "returns_cancellation_exchange": 0,
    "privacy_policy": 0,
    "shipping_delivery": 0,
    "contact_us": 1,
    "catalogue": 9,
    "urls_not_reachable": 0,
    "Unreachable_via_tool": 3
  },
  "total_reachable_urls": 15,
  "priority_reachable_urls": 11,
  "total_unreachable_urls": 0,
  "unreachable_percentage": "0.0%"
}
[2025-07-28 00:52:29][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Some URLs are reachable - continuing with normal flow
{
  "total_reachable_urls": 15,
  "priority_urls_found": 11,
  "decision": "NORMAL_FLOW_CONTINUES",
  "reachable_categories": [
    "home_page",
    "about_us",
    "contact_us",
    "catalogue"
  ]
}
[2025-07-28 00:52:29][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Starting priority URL filtering
{
  "input_categories": [
    "home_page",
    "about_us",
    "terms_and_condition",
    "returns_cancellation_exchange",
    "privacy_policy",
    "shipping_delivery",
    "contact_us",
    "catalogue",
    "urls_not_reachable",
    "Unreachable_via_tool"
  ],
  "priority_categories_defined": [
    "about_us",
    "catalogue",
    "products",
    "home_page"
  ]
}
[2025-07-28 00:52:29][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Filtering priority URLs for MCC analysis - input analysis
{
  "total_categories": 10,
  "input_category_counts": {
    "home_page": 1,
    "about_us": 1,
    "terms_and_condition": 0,
    "returns_cancellation_exchange": 0,
    "privacy_policy": 0,
    "shipping_delivery": 0,
    "contact_us": 1,
    "catalogue": 9,
    "urls_not_reachable": 0,
    "Unreachable_via_tool": 3
  },
  "priority_categories": [
    "about_us",
    "catalogue",
    "products",
    "home_page"
  ],
  "max_urls_for_mcc": 18
}
[2025-07-28 00:52:29][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: URLs per category calculated: 18
[2025-07-28 00:52:29][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] DEBUG: Processing category 'home_page': 1 URLs
{
  "category": "home_page",
  "url_count": 1,
  "is_priority": true,
  "has_urls": true
}
[2025-07-28 00:52:29][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: ✅ Added 1 URLs for priority category 'home_page'
{
  "category": "home_page",
  "added_urls": 1,
  "total_available": 1,
  "urls_per_category_limit": 18
}
[2025-07-28 00:52:29][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] DEBUG: Processing category 'about_us': 1 URLs
{
  "category": "about_us",
  "url_count": 1,
  "is_priority": true,
  "has_urls": true
}
[2025-07-28 00:52:29][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: ✅ Added 1 URLs for priority category 'about_us'
{
  "category": "about_us",
  "added_urls": 1,
  "total_available": 1,
  "urls_per_category_limit": 18
}
[2025-07-28 00:52:29][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] DEBUG: Processing category 'terms_and_condition': 0 URLs
{
  "category": "terms_and_condition",
  "url_count": 0,
  "is_priority": false,
  "has_urls": false
}
[2025-07-28 00:52:29][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] DEBUG: Processing category 'returns_cancellation_exchange': 0 URLs
{
  "category": "returns_cancellation_exchange",
  "url_count": 0,
  "is_priority": false,
  "has_urls": false
}
[2025-07-28 00:52:29][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] DEBUG: Processing category 'privacy_policy': 0 URLs
{
  "category": "privacy_policy",
  "url_count": 0,
  "is_priority": false,
  "has_urls": false
}
[2025-07-28 00:52:29][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] DEBUG: Processing category 'shipping_delivery': 0 URLs
{
  "category": "shipping_delivery",
  "url_count": 0,
  "is_priority": false,
  "has_urls": false
}
[2025-07-28 00:52:29][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] DEBUG: Processing category 'contact_us': 1 URLs
{
  "category": "contact_us",
  "url_count": 1,
  "is_priority": false,
  "has_urls": true
}
[2025-07-28 00:52:29][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] DEBUG: Processing category 'catalogue': 9 URLs
{
  "category": "catalogue",
  "url_count": 9,
  "is_priority": true,
  "has_urls": true
}
[2025-07-28 00:52:29][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: ✅ Added 9 URLs for priority category 'catalogue'
{
  "category": "catalogue",
  "added_urls": 9,
  "total_available": 9,
  "urls_per_category_limit": 18
}
[2025-07-28 00:52:29][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] DEBUG: Processing category 'urls_not_reachable': 0 URLs
{
  "category": "urls_not_reachable",
  "url_count": 0,
  "is_priority": false,
  "has_urls": false
}
[2025-07-28 00:52:29][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] DEBUG: Processing category 'Unreachable_via_tool': 3 URLs
{
  "category": "Unreachable_via_tool",
  "url_count": 3,
  "is_priority": false,
  "has_urls": true
}
[2025-07-28 00:52:29][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Priority URLs filtered
{
  "priority_categories": [
    "home_page",
    "about_us",
    "catalogue"
  ],
  "total_priority_urls": 11,
  "fallback_applied": false
}
[2025-07-28 00:52:29][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Priority URL filtering completed
{
  "priority_url_counts": {
    "home_page": 1,
    "about_us": 1,
    "catalogue": 9
  },
  "total_priority_urls": 11
}
[2025-07-28 00:52:29][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Data preparation completed
{
  "total_classified_urls": 15,
  "priority_urls_count": 11
}
[2025-07-28 00:52:29][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Successfully unpacked data preparation result
{
  "classified_urls_type": "<class 'dict'>",
  "priority_urls_type": "<class 'dict'>",
  "classified_urls_is_none": false,
  "priority_urls_is_none": false
}
[2025-07-28 00:52:29][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Step 2 completed in 24.36 seconds
[2025-07-28 00:52:29][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Step 3: Starting MCC classification
{
  "website": "https://bakingcart.com/",
  "priority_url_categories": [
    "home_page",
    "about_us",
    "catalogue"
  ],
  "total_priority_urls": 11
}
[2025-07-28 00:52:29][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Starting MCC classification
{
  "website": "https://bakingcart.com/",
  "priority_categories": [
    "home_page",
    "about_us",
    "catalogue"
  ]
}
[2025-07-28 00:52:29][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: 🔍 Extracting text content from priority URLs for analysis
[2025-07-28 00:52:29][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: 📄 Extracting text from home_page: https://bakingcart.com/
[2025-07-28 00:52:29][text_extraction][NO_REF] INFO: Starting text extraction
{
  "url": "https://bakingcart.com/",
  "timeout": 60
}2025-07-28 00:53:19,023 - asyncio - ERROR - Future exception was never retrieved
future: <Future finished exception=TargetClosedError('Target page, context or browser has been closed')>
playwright._impl._errors.TargetClosedError: Target page, context or browser has been closed
2025-07-28 00:53:24,141 - google_genai.models - INFO - AFC is enabled with max remote calls: 20000.
2025-07-28 00:53:29,012 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent "HTTP/1.1 200 OK"
2025-07-28 00:53:29,018 - google_genai.models - INFO - AFC remote call 1 is done.

[2025-07-28 00:52:38][text_extraction][NO_REF] WARNING: JavaScript security challenge detected
{
  "url": "https://www.epaal.in/products/spiral-triple-color-2-pcs"
}
[2025-07-28 00:52:38][text_extraction][NO_REF] WARNING: Page appears to be blocked with direct connection
{
  "url": "https://www.epaal.in/products/spiral-triple-color-2-pcs",
  "retry": 0
}
[2025-07-28 00:52:38][text_extraction][NO_REF] INFO: Retrying direct connection in 1.50 seconds
[2025-07-28 00:52:41][text_extraction][NO_REF] WARNING: JavaScript security challenge detected
{
  "url": "https://bakingcart.com/"
}
[2025-07-28 00:52:41][text_extraction][NO_REF] WARNING: Page appears to be blocked with direct connection
{
  "url": "https://bakingcart.com/",
  "retry": 0
}
[2025-07-28 00:52:41][text_extraction][NO_REF] INFO: Retrying direct connection in 1.50 seconds
[2025-07-28 00:52:53][text_extraction][NO_REF] WARNING: JavaScript security challenge detected
{
  "url": "https://bakingcart.com/"
}
[2025-07-28 00:52:53][text_extraction][NO_REF] WARNING: Page appears to be blocked with direct connection
{
  "url": "https://bakingcart.com/",
  "retry": 1
}
[2025-07-28 00:52:53][text_extraction][NO_REF] INFO: Attempting proxy connection for text extraction as fallback
[2025-07-28 00:52:53][text_extraction][NO_REF] INFO: Using webshare proxy with valid credentials
[2025-07-28 00:52:53][text_extraction][NO_REF] WARNING: JavaScript security challenge detected
{
  "url": "https://www.epaal.in/products/spiral-triple-color-2-pcs"
}
[2025-07-28 00:52:53][text_extraction][NO_REF] WARNING: Page appears to be blocked with direct connection
{
  "url": "https://www.epaal.in/products/spiral-triple-color-2-pcs",
  "retry": 1
}
[2025-07-28 00:52:53][text_extraction][NO_REF] INFO: Attempting proxy connection for text extraction as fallback
[2025-07-28 00:52:53][text_extraction][NO_REF] INFO: Using webshare proxy with valid credentials
[2025-07-28 00:53:18][text_extraction][NO_REF] ERROR: Text extraction timed out after 60 seconds for URL: https://www.epaal.in/products/spiral-triple-color-2-pcs
[2025-07-28 00:53:18][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] WARNING: ⚠️ Playwright extraction insufficient for catalogue, trying requests method
[2025-07-28 00:53:19][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: ✅ Text extracted from catalogue: 5000 characters
[2025-07-28 00:53:19][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: 📊 Text extraction completed: 3 categories with content
[2025-07-28 00:53:19][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Website information attempt 1
{
  "website": "https://www.epaal.in/"
}
[2025-07-28 00:53:19][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: 📊 URLs being passed to Gemini for website summary
{
  "total_urls_count": 8,
  "urls_breakdown_by_category": {
    "home_page": 1,
    "about_us": 1,
    "catalogue": 6
  },
  "priority_urls_structure": {
    "home_page": [
      "https://www.epaal.in/"
    ],
    "about_us": [
      "https://www.epaal.in/pages/about-us"
    ],
    "catalogue": [
      "https://www.epaal.in/products/spiral-triple-color-2-pcs",
      "https://www.epaal.in/collections/earbud-case"
    ]
  },
  "max_urls_for_mcc_limit": 18,
  "extracted_content_categories": [
    "home_page",
    "about_us",
    "catalogue"
  ],
  "total_extracted_text_length": 15000
}
[2025-07-28 00:53:19][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: ✅ Using extracted text content for website analysis
[2025-07-28 00:53:19][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Calling Gemini API for website information...
[2025-07-28 00:53:24][mcc_analysis_unknown][NO_REF] INFO: Starting Gemini API call
{
  "model": "gemini-2.5-flash",
  "timeout": 90,
  "max_retries": 3,
  "prompt_length": 10600,
  "context": {
    "task_type": "mcc_analysis"
  }
}
[2025-07-28 00:53:24][mcc_analysis_unknown][NO_REF] INFO: Gemini API attempt 1/3
[2025-07-28 00:53:29][mcc_analysis_unknown][NO_REF] INFO: Gemini API Usage: cache_tokens_details=None cached_content_token_count=None candidates_token_count=370 candidates_tokens_details=None prompt_token_count=2651 prompt_tokens_details=[ModalityTokenCount(
  modality=<MediaModality.TEXT: 'TEXT'>,
  token_count=2651
)] thoughts_token_count=532 tool_use_prompt_token_count=None tool_use_prompt_tokens_details=None total_token_count=3553 traffic_type=None
[2025-07-28 00:53:29][mcc_analysis_unknown][NO_REF] INFO: Gemini API call successful
{
  "attempt": 1,
  "response_length": 1448,
  "finish_reason": "STOP"
}
[2025-07-28 00:53:29][text_extraction][NO_REF] ERROR: Failed to navigate to URL with proxy for text extraction (timeout)
{
  "error": "{'error': {'error': 'Page.goto: Timeout 36000ms exceeded.\\nCall log:\\n  - navigating to \"https://bakingcart.com/\", waiting until \"load\"\\n', 'url': 'https://bakingcart.com/', 'retry': 0}}",
  "error_type": "dict"
}
Traceback for analysis text_extraction:
Error printing traceback: 'dict' object has no attribute '__traceback__'
[2025-07-28 00:53:29][text_extraction][NO_REF] ERROR: Failed to access https://bakingcart.com/ for text extraction with both direct and proxy connections
[2025-07-28 00:53:29][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] WARNING: ⚠️ Playwright extraction insufficient for home_page, trying requests method
[2025-07-28 00:53:30][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: ✅ Text extracted from home_page: 5000 characters
[2025-07-28 00:53:30][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: 📄 Extracting text from about_us: https://bakingcart.com/pages/about-us
[2025-07-28 00:53:30][text_extraction][NO_REF] INFO: Starting text extraction
{
  "url": "https://bakingcart.com/pages/about-us",
  "timeout": 60
}
[2025-07-28 00:53:34][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Website info response length: 1448
[2025-07-28 00:53:34][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Received Gemini API response for website information
[2025-07-28 00:53:34][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Successfully parsed JSON response
{
  "keys": [
    "is_valid_website",
    "website_redirection",
    "product_services",
    "line_of_business",
    "customers",
    "website_description"
  ]
}
[2025-07-28 00:53:34][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: 📋 WEBSITE SUMMARY OUTPUT FROM GEMINI
{
  "website": "https://www.epaal.in/",
  "total_urls_analyzed": 8,
  "summary_content": {
    "product_services": "Epaal offers a wide range of smartwatch and mobile accessories, including: Cable & Adapter Protectors (e.g., Spiral Triple Color, Spiral Metallic Color), Watch Straps for various brands and models such as Xiaomi Mi Band (6, 5, 4, 3, 2), Honor Band (6, 5, 4), Realme Watch (original, 2, 2 Pro, S, S Pro, 3), and Apple Watch (38mm/40mm, 42mm/44mm, 19/20/22 mm sizes). They also sell Car Mobile Holders, Selfie Sticks, Earbud Cases, Home Electronics (Security Cameras, Bluetooth Speakers, Bluetooth earphones), Baby Care items (Teethers, Bibs), Home & Kitchen products (Keychains), Computer Accessories (Mouse, Bluetooth Speakers), and Apparels. Specific products mentioned include the Epaal Magnetic USB Charging Cable for Honor Band 6 / Huawei Band 6 / Honor Watch ES/Huawei 4X.",
    "line_of_business": "E-commerce / Online Retail of Consumer Electronics and Accessories",
    "customers": "B2C (Business-to-Consumer), targeting individual shopping enthusiasts looking for mobile and smartwatch accessories, as well as other consumer goods.",
    "website_description": "Epaal is an online multi-brand megastore established in June 2013, specializing in smartwatches and mobile accessories. The website aims to provide a reliable, promising, and safe online shopping experience with a focus on customer satisfaction and transparent feedback."
  },
  "summary_lengths": {
    "product_services_chars": 777,
    "line_of_business_chars": 66,
    "customers_chars": 149,
    "website_description_chars": 270
  }
}
[2025-07-28 00:53:34][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Website information extracted successfully
{
  "info_keys": [
    "product_services",
    "line_of_business",
    "customers",
    "website_description"
  ]
}2025-07-28 00:53:39,060 - google_genai.models - INFO - AFC is enabled with max remote calls: 20000.
2025-07-28 00:53:56,772 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent "HTTP/1.1 200 OK"
2025-07-28 00:53:56,781 - google_genai.models - INFO - AFC remote call 1 is done.

[2025-07-28 00:53:34][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: MCC classification attempt 1
{
  "website": "https://www.epaal.in/"
}
[2025-07-28 00:53:34][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Loading CSV data files...
[2025-07-28 00:53:34][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Loaded special_mccs.csv with 57 rows
[2025-07-28 00:53:34][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Loaded combined_mcc.csv with 303 rows
[2025-07-28 00:53:34][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: CSV data processing completed
[2025-07-28 00:53:34][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Calling Gemini API for MCC classification...
[2025-07-28 00:53:36][text_extraction][NO_REF] WARNING: JavaScript security challenge detected
{
  "url": "https://bakingcart.com/pages/about-us"
}
[2025-07-28 00:53:36][text_extraction][NO_REF] WARNING: Page appears to be blocked with direct connection
{
  "url": "https://bakingcart.com/pages/about-us",
  "retry": 0
}
[2025-07-28 00:53:36][text_extraction][NO_REF] INFO: Retrying direct connection in 1.50 seconds
[2025-07-28 00:53:39][mcc_analysis_unknown][NO_REF] INFO: Starting Gemini API call
{
  "model": "gemini-2.5-flash",
  "timeout": 90,
  "max_retries": 3,
  "prompt_length": 223361,
  "context": {
    "task_type": "mcc_analysis"
  }
}
[2025-07-28 00:53:39][mcc_analysis_unknown][NO_REF] INFO: Gemini API attempt 1/3
[2025-07-28 00:53:44][text_extraction][NO_REF] WARNING: JavaScript security challenge detected
{
  "url": "https://bakingcart.com/pages/about-us"
}
[2025-07-28 00:53:44][text_extraction][NO_REF] WARNING: Page appears to be blocked with direct connection
{
  "url": "https://bakingcart.com/pages/about-us",
  "retry": 1
}
[2025-07-28 00:53:44][text_extraction][NO_REF] INFO: Attempting proxy connection for text extraction as fallback
[2025-07-28 00:53:44][text_extraction][NO_REF] INFO: Using webshare proxy with valid credentials
[2025-07-28 00:53:56][mcc_analysis_unknown][NO_REF] INFO: Gemini API Usage: cache_tokens_details=None cached_content_token_count=None candidates_token_count=193 candidates_tokens_details=None prompt_token_count=46747 prompt_tokens_details=[ModalityTokenCount(
  modality=<MediaModality.TEXT: 'TEXT'>,
  token_count=46747
)] thoughts_token_count=3217 tool_use_prompt_token_count=None tool_use_prompt_tokens_details=None total_token_count=50157 traffic_type=None
[2025-07-28 00:53:56][mcc_analysis_unknown][NO_REF] INFO: Gemini API call successful
{
  "attempt": 1,
  "response_length": 785,
  "finish_reason": "STOP"
}
[2025-07-28 00:54:01][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Received Gemini API response for MCC classification
[2025-07-28 00:54:01][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Successfully parsed JSON response
{
  "keys": [
    "mcc",
    "business_desc",
    "business_category",
    "reason",
    "website"
  ]
}
[2025-07-28 00:54:01][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: MCC classification retrieved successfully
{
  "mcc": "5311",
  "business_desc": "Epaal is an online multi-brand megastore specializing in smartwatches, mobile accessories, home electronics, computer accessories, baby care items, home & kitchen products, and apparel.",
  "business_category": "Departmental Store",
  "reason": "Epaal is described as an 'online multi-brand megastore' offering a wide range of products including 'smartwatch and mobile accessories', 'Home Electronics', 'Computer Accessories', 'Baby Care items', 'Home & Kitchen products', and 'Apparels'. This aligns perfectly with MCC 5311 in the special_mcc_data, which describes 'multiple categories business - Departmental Store' selling 'apparel, home furnishings, electronics, mobile phones and mobile accessories, and housewares'."
}
[2025-07-28 00:54:01][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Step 3: MCC classification completed in 216.73 seconds
{
  "website_info_available": true,
  "mcc_info_available": true,
  "processing_time": "216.73s"
}
[2025-07-28 00:54:01][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: ✅ Website content appears valid - continuing with normal flow
{
  "inactive_fields": 0,
  "valid_fields": 4,
  "threshold_check": "passed"
}
[2025-07-28 00:54:01][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Step 4: Saving results to database
[2025-07-28 00:54:01][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Saving MCC analysis results to database
{
  "website": "https://www.epaal.in/"
}
2025-07-28 00:54:01,783 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = ?
2025-07-28 00:54:01,783 - sqlalchemy.engine.Engine - INFO - SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = ?
2025-07-28 00:54:01,783 INFO sqlalchemy.engine.Engine [cached since 246.1s ago] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42',)
2025-07-28 00:54:01,783 - sqlalchemy.engine.Engine - INFO - [cached since 246.1s ago] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42',)
2025-07-28 00:54:01,784 INFO sqlalchemy.engine.Engine UPDATE mcc_analysis_gemini SET mcc_code=?, business_category=?, business_description=?, reasoning=?, completed_at=?, details=?, processing_status=? WHERE mcc_analysis_gemini.id = ?
2025-07-28 00:54:01,784 - sqlalchemy.engine.Engine - INFO - UPDATE mcc_analysis_gemini SET mcc_code=?, business_category=?, business_description=?, reasoning=?, completed_at=?, details=?, processing_status=? WHERE mcc_analysis_gemini.id = ?
2025-07-28 00:54:01,784 INFO sqlalchemy.engine.Engine [generated in 0.00015s] ('5311', 'Departmental Store', '', "Epaal is described as an 'online multi-brand megastore' offering a wide range of products including 'smartwatch and mobile accessories', 'Home Electr ... (177 characters truncated) ... ple categories business - Departmental Store' selling 'apparel, home furnishings, electronics, mobile phones and mobile accessories, and housewares'.", '2025-07-28T00:54:01.784006Z', '{"website": "https://www.epaal.in/", "scrape_request_ref_id": "ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42", "website_info": {"product_services": "Ep ... (3387 characters truncated) ... lysis", "content_availability_status": "content_available", "fallback_method_used": false, "text_extraction_used": false, "insufficient_data": false}', 'COMPLETED', 25)
2025-07-28 00:54:01,784 - sqlalchemy.engine.Engine - INFO - [generated in 0.00015s] ('5311', 'Departmental Store', '', "Epaal is described as an 'online multi-brand megastore' offering a wide range of products including 'smartwatch and mobile accessories', 'Home Electr ... (177 characters truncated) ... ple categories business - Departmental Store' selling 'apparel, home furnishings, electronics, mobile phones and mobile accessories, and housewares'.", '2025-07-28T00:54:01.784006Z', '{"website": "https://www.epaal.in/", "scrape_request_ref_id": "ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42", "website_info": {"product_services": "Ep ... (3387 characters truncated) ... lysis", "content_availability_status": "content_available", "fallback_method_used": false, "text_extraction_used": false, "insufficient_data": false}', 'COMPLETED', 25)
2025-07-28 00:54:01,785 INFO sqlalchemy.engine.Engine COMMIT
2025-07-28 00:54:01,785 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-28 00:54:02,092 - httpx - INFO - HTTP Request: PATCH https://bffapi.biztel.ai/api/mcc/results "HTTP/1.1 401 "
[2025-07-28 00:54:01][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: MCC analysis results saved successfully
{
  "analysis_id": 25
}
[2025-07-28 00:54:01][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: 🚀 Preparing MCC results webhook with correct format
{
  "scrape_request_ref_id": "ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42",
  "website": "https://www.epaal.in/",
  "analysis_id": 25
}
[2025-07-28 00:54:01][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: 📦 MCC WEBHOOK PAYLOAD CONSTRUCTION - STEP BY STEP
{
  "construction_step": "1_mcc_payload_created",
  "timestamp": "2025-07-28T00:54:01.797571",
  "website": "https://www.epaal.in/",
  "scrape_request_ref_id": "ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42",
  "mcc_code": "5311",
  "business_category": "Departmental Store",
  "business_description_length": 0,
  "payload_keys": [
    "website",
    "createdDate",
    "status",
    "scrapeRequestUuid",
    "mcc",
    "manualMcc",
    "businessCategory",
    "businessDescription"
  ],
  "payload_structure": {
    "website": "https://www.epaal.in/",
    "scrapeRequestUuid": "ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42",
    "status": "COMPLETED",
    "mcc": 5311,
    "manualMcc": -1,
    "businessCategory": "Departmental Store",
    "businessDescription_length": 0,
    "createdDate": "2025-07-28T00:54:01.797560Z"
  },
  "complete_payload_json": {
    "website": "https://www.epaal.in/",
    "createdDate": "2025-07-28T00:54:01.797560Z",
    "status": "COMPLETED",
    "scrapeRequestUuid": "ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42",
    "mcc": 5311,
    "manualMcc": -1,
    "businessCategory": "Departmental Store",
    "businessDescription": ""
  }
}
[2025-07-28 00:54:01][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: 📤 Sending MCC PATCH request with clean headers - DETAILED DEBUG
{
  "url": "https://bffapi.biztel.ai/api/mcc/results",
  "method": "PATCH",
  "payload": {
    "website": "https://www.epaal.in/",
    "createdDate": "2025-07-28T00:54:01.797560Z",
    "status": "COMPLETED",
    "scrapeRequestUuid": "ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42",
    "mcc": 5311,
    "manualMcc": -1,
    "businessCategory": "Departmental Store",
    "businessDescription": ""
  },
  "headers_being_sent": {
    "X-API-KEY": "12345678",
    "Content-Type": "application/json"
  },
  "api_key_in_headers": "12345678",
  "content_type_in_headers": "application/json",
  "api_key_length": 8
}
[2025-07-28 00:54:01][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: 🔥 MCC PATCH REQUEST - FINAL TRANSMISSION
{
  "construction_step": "2_sending_mcc_request",
  "timestamp": "2025-07-28T00:54:01.802617",
  "about_to_send": true,
  "final_url": "https://bffapi.biztel.ai/api/mcc/results",
  "final_method": "PATCH",
  "final_headers": {
    "X-API-KEY": "12345678",
    "Content-Type": "application/json"
  },
  "final_payload": {
    "website": "https://www.epaal.in/",
    "createdDate": "2025-07-28T00:54:01.797560Z",
    "status": "COMPLETED",
    "scrapeRequestUuid": "ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42",
    "mcc": 5311,
    "manualMcc": -1,
    "businessCategory": "Departmental Store",
    "businessDescription": ""
  },
  "final_timeout": 30.0,
  "curl_equivalent": "curl -X PATCH 'https://bffapi.biztel.ai/api/mcc/results' -H 'Content-Type: application/json' -H 'X-API-KEY: 12345678' -d '{\"website\": \"https://www.epaal.in/\", \"createdDate\": \"2025-07-28T00:54:01.797560Z\", \"status\": \"COMPLETED\", \"scrapeRequestUuid\": \"ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42\", \"mcc\": 5311, \"manualMcc\": -1, \"businessCategory\": \"Departmental Store\", \"businessDescription\": \"\"}'"
}
[2025-07-28 00:54:02][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: ⚡ MCC PATCH RESPONSE - IMMEDIATE RESULT
{
  "construction_step": "3_mcc_response_received",
  "timestamp": "2025-07-28T00:54:02.092637",
  "response_status_code": 401,
  "response_headers": {
    "server": "nginx/1.22.1",
    "date": "Sun, 27 Jul 2025 19:24:02 GMT",
    "content-length": "0",
    "connection": "keep-alive",
    "vary": "Origin, Access-Control-Request-Method, Access-Control-Request-Headers",
    "x-content-type-options": "nosniff",
    "x-xss-protection": "0",
    "cache-control": "no-cache, no-store, max-age=0, must-revalidate",
    "pragma": "no-cache",
    "expires": "0",
    "x-frame-options": "DENY",
    "www-authenticate": "Basic realm=\"Realm\""
  },
  "response_text": "",
  "response_success": false,
  "response_size": 0,
  "request_url": "https://bffapi.biztel.ai/api/mcc/results"
}
[2025-07-28 00:54:02][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: 📥 MCC PATCH response received
{
  "status_code": 401,
  "response_text": "",
  "response_headers": {
    "server": "nginx/1.22.1",
    "date": "Sun, 27 Jul 2025 19:24:02 GMT",
    "content-length": "0",
    "connection": "keep-alive",
    "vary": "Origin, Access-Control-Request-Method, Access-Control-Request-Headers",
    "x-content-type-options": "nosniff",
    "x-xss-protection": "0",
    "cache-control": "no-cache, no-store, max-age=0, must-revalidate",
    "pragma": "no-cache",
    "expires": "0",
    "x-frame-options": "DENY",
    "www-authenticate": "Basic realm=\"Realm\""
  },
  "url": "https://bffapi.biztel.ai/api/mcc/results"
}
[2025-07-28 00:54:02][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] ERROR: ❌ MCC results webhook failed - Authentication error
{
  "error": "{'status_code': 401, 'response': '', 'url': 'https://bffapi.biztel.ai/api/mcc/results', 'error': 'Invalid API key - check BIZTEL_API_KEY in .env file', 'api_key_length': 8}",
  "error_type": "dict"
}
Traceback for analysis ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42:
Error printing traceback: 'dict' object has no attribute '__traceback__'
[2025-07-28 00:54:02][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: 🔄 Attempting to send default values despite authentication error
[2025-07-28 00:54:02][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: 📋 Created default payload for authentication error
{
  "payload": {
    "website": "https://www.epaal.in/",
    "createdDate": "2025-07-28T00:54:02.092786Z",
    "status": "FAILED",
    "scrapeRequestUuid": "ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42",
    "mcc": -1,
    "manualMcc": -1,
    "businessCategory": "authentication_error",
    "businessDescription": "webhook authentication failed"
  }
}
[2025-07-28 00:54:02][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] WARNING: ⚠️ MCC results webhook failed
{
  "analysis_id": 25
}
[2025-07-28 00:54:02][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Saving detailed MCC URL classification results
[2025-07-28 00:54:02][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Saving MCC URL classification results
{
  "analysis_id": 25,
  "soft_categories": 16,
  "hard_categories": 10
}
2025-07-28 00:54:02,093 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-28 00:54:02,093 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 00:54:02,093 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-28 00:54:02,093 - sqlalchemy.engine.Engine - INFO - SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-28 00:54:02,093 INFO sqlalchemy.engine.Engine [cached since 246.5s ago] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42',)
2025-07-28 00:54:02,093 - sqlalchemy.engine.Engine - INFO - [cached since 246.5s ago] ('ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42',)
2025-07-28 00:54:02,097 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:54:02,097 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:54:02,097 INFO sqlalchemy.engine.Engine [generated in 0.00021s (insertmanyvalues) 1/16 (ordered; batch not supported)] (25, 'ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/pages/cancellation-policy', 1, 'returns_cancellation_exchange', 'returns_cancellation_exchange', 'returns_cancellation_exchange', 0, 0, '2025-07-28T00:54:02.094799Z', 'default')
2025-07-28 00:54:02,097 - sqlalchemy.engine.Engine - INFO - [generated in 0.00021s (insertmanyvalues) 1/16 (ordered; batch not supported)] (25, 'ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/pages/cancellation-policy', 1, 'returns_cancellation_exchange', 'returns_cancellation_exchange', 'returns_cancellation_exchange', 0, 0, '2025-07-28T00:54:02.094799Z', 'default')
2025-07-28 00:54:02,098 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:54:02,098 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:54:02,098 INFO sqlalchemy.engine.Engine [insertmanyvalues 2/16 (ordered; batch not supported)] (25, 'ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/products/spiral-triple-color-2-pcs', 1, 'products', 'catalogue', 'catalogue', 1, 0, '2025-07-28T00:54:02.094968Z', 'default')
2025-07-28 00:54:02,098 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 2/16 (ordered; batch not supported)] (25, 'ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/products/spiral-triple-color-2-pcs', 1, 'products', 'catalogue', 'catalogue', 1, 0, '2025-07-28T00:54:02.094968Z', 'default')
2025-07-28 00:54:02,098 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:54:02,098 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:54:02,098 INFO sqlalchemy.engine.Engine [insertmanyvalues 3/16 (ordered; batch not supported)] (25, 'ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/policies/refund-policy', 1, 'returns_cancellation_exchange', 'Unreachable_via_tool', 'Unreachable_via_tool', 0, 0, '2025-07-28T00:54:02.095066Z', 'default')
2025-07-28 00:54:02,098 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 3/16 (ordered; batch not supported)] (25, 'ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/policies/refund-policy', 1, 'returns_cancellation_exchange', 'Unreachable_via_tool', 'Unreachable_via_tool', 0, 0, '2025-07-28T00:54:02.095066Z', 'default')
2025-07-28 00:54:02,098 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:54:02,098 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:54:02,098 INFO sqlalchemy.engine.Engine [insertmanyvalues 4/16 (ordered; batch not supported)] (25, 'ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 1, 'home_page', 'home_page', 'home_page', 1, 1, '2025-07-28T00:54:02.095156Z', 'default')
2025-07-28 00:54:02,098 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 4/16 (ordered; batch not supported)] (25, 'ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/', 1, 'home_page', 'home_page', 'home_page', 1, 1, '2025-07-28T00:54:02.095156Z', 'default')
2025-07-28 00:54:02,098 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:54:02,098 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:54:02,098 INFO sqlalchemy.engine.Engine [insertmanyvalues 5/16 (ordered; batch not supported)] (25, 'ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/pages/returns', 1, 'returns_cancellation_exchange', 'returns_cancellation_exchange', 'returns_cancellation_exchange', 0, 0, '2025-07-28T00:54:02.095244Z', 'default')
2025-07-28 00:54:02,098 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 5/16 (ordered; batch not supported)] (25, 'ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/pages/returns', 1, 'returns_cancellation_exchange', 'returns_cancellation_exchange', 'returns_cancellation_exchange', 0, 0, '2025-07-28T00:54:02.095244Z', 'default')
2025-07-28 00:54:02,098 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:54:02,098 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:54:02,098 INFO sqlalchemy.engine.Engine [insertmanyvalues 6/16 (ordered; batch not supported)] (25, 'ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/pages/privacy-policy', 1, 'privacy_policy', 'privacy_policy', 'privacy_policy', 0, 0, '2025-07-28T00:54:02.095331Z', 'default')
2025-07-28 00:54:02,098 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 6/16 (ordered; batch not supported)] (25, 'ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/pages/privacy-policy', 1, 'privacy_policy', 'privacy_policy', 'privacy_policy', 0, 0, '2025-07-28T00:54:02.095331Z', 'default')
2025-07-28 00:54:02,098 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:54:02,098 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:54:02,098 INFO sqlalchemy.engine.Engine [insertmanyvalues 7/16 (ordered; batch not supported)] (25, 'ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/pages/shipping-policy', 1, 'shipping_delivery', 'shipping_delivery', 'shipping_delivery', 0, 0, '2025-07-28T00:54:02.095423Z', 'default')
2025-07-28 00:54:02,098 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 7/16 (ordered; batch not supported)] (25, 'ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/pages/shipping-policy', 1, 'shipping_delivery', 'shipping_delivery', 'shipping_delivery', 0, 0, '2025-07-28T00:54:02.095423Z', 'default')
2025-07-28 00:54:02,098 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:54:02,098 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:54:02,098 INFO sqlalchemy.engine.Engine [insertmanyvalues 8/16 (ordered; batch not supported)] (25, 'ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/pages/terms-conditions', 1, 'terms_and_condition', 'terms_and_condition', 'terms_and_condition', 0, 0, '2025-07-28T00:54:02.095510Z', 'default')
2025-07-28 00:54:02,098 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 8/16 (ordered; batch not supported)] (25, 'ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/pages/terms-conditions', 1, 'terms_and_condition', 'terms_and_condition', 'terms_and_condition', 0, 0, '2025-07-28T00:54:02.095510Z', 'default')
2025-07-28 00:54:02,098 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:54:02,098 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:54:02,098 INFO sqlalchemy.engine.Engine [insertmanyvalues 9/16 (ordered; batch not supported)] (25, 'ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/pages/contact-us', 1, 'contact_us', 'contact_us', 'contact_us', 0, 0, '2025-07-28T00:54:02.095598Z', 'default')
2025-07-28 00:54:02,098 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 9/16 (ordered; batch not supported)] (25, 'ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/pages/contact-us', 1, 'contact_us', 'contact_us', 'contact_us', 0, 0, '2025-07-28T00:54:02.095598Z', 'default')
2025-07-28 00:54:02,098 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:54:02,098 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:54:02,099 INFO sqlalchemy.engine.Engine [insertmanyvalues 10/16 (ordered; batch not supported)] (25, 'ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/collections/earbud-case', 1, 'products', 'catalogue', 'catalogue', 1, 0, '2025-07-28T00:54:02.095689Z', 'default')
2025-07-28 00:54:02,099 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 10/16 (ordered; batch not supported)] (25, 'ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/collections/earbud-case', 1, 'products', 'catalogue', 'catalogue', 1, 0, '2025-07-28T00:54:02.095689Z', 'default')
2025-07-28 00:54:02,099 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:54:02,099 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:54:02,099 INFO sqlalchemy.engine.Engine [insertmanyvalues 11/16 (ordered; batch not supported)] (25, 'ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/products/epaal-tpu-silicon-band-strap-for-huawei-honor-band-5-honor-band-4', 1, 'products', 'catalogue', 'catalogue', 1, 0, '2025-07-28T00:54:02.095776Z', 'default')
2025-07-28 00:54:02,099 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 11/16 (ordered; batch not supported)] (25, 'ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/products/epaal-tpu-silicon-band-strap-for-huawei-honor-band-5-honor-band-4', 1, 'products', 'catalogue', 'catalogue', 1, 0, '2025-07-28T00:54:02.095776Z', 'default')
2025-07-28 00:54:02,099 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:54:02,099 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:54:02,099 INFO sqlalchemy.engine.Engine [insertmanyvalues 12/16 (ordered; batch not supported)] (25, 'ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/pages/about-us', 1, 'about_us', 'about_us', 'about_us', 1, 0, '2025-07-28T00:54:02.095862Z', 'default')
2025-07-28 00:54:02,099 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 12/16 (ordered; batch not supported)] (25, 'ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/pages/about-us', 1, 'about_us', 'about_us', 'about_us', 1, 0, '2025-07-28T00:54:02.095862Z', 'default')
2025-07-28 00:54:02,099 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:54:02,099 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:54:02,099 INFO sqlalchemy.engine.Engine [insertmanyvalues 13/16 (ordered; batch not supported)] (25, 'ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/products/epaal-protective-case-for-xiaomi-power-bank-4i-20000mah-33w-power-bank', 1, 'products', 'catalogue', 'catalogue', 1, 0, '2025-07-28T00:54:02.095950Z', 'default')
2025-07-28 00:54:02,099 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 13/16 (ordered; batch not supported)] (25, 'ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/products/epaal-protective-case-for-xiaomi-power-bank-4i-20000mah-33w-power-bank', 1, 'products', 'catalogue', 'catalogue', 1, 0, '2025-07-28T00:54:02.095950Z', 'default')
2025-07-28 00:54:02,099 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:54:02,099 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:54:02,099 INFO sqlalchemy.engine.Engine [insertmanyvalues 14/16 (ordered; batch not supported)] (25, 'ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/collections/new-collection', 1, 'catalogue', 'catalogue', 'catalogue', 1, 0, '2025-07-28T00:54:02.096036Z', 'default')
2025-07-28 00:54:02,099 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 14/16 (ordered; batch not supported)] (25, 'ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/collections/new-collection', 1, 'catalogue', 'catalogue', 'catalogue', 1, 0, '2025-07-28T00:54:02.096036Z', 'default')
2025-07-28 00:54:02,099 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:54:02,099 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:54:02,099 INFO sqlalchemy.engine.Engine [insertmanyvalues 15/16 (ordered; batch not supported)] (25, 'ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/policies/terms-of-service', 1, 'terms_and_condition', 'Unreachable_via_tool', 'Unreachable_via_tool', 0, 0, '2025-07-28T00:54:02.096122Z', 'default')
2025-07-28 00:54:02,099 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 15/16 (ordered; batch not supported)] (25, 'ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/policies/terms-of-service', 1, 'terms_and_condition', 'Unreachable_via_tool', 'Unreachable_via_tool', 0, 0, '2025-07-28T00:54:02.096122Z', 'default')
2025-07-28 00:54:02,099 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:54:02,099 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:54:02,099 INFO sqlalchemy.engine.Engine [insertmanyvalues 16/16 (ordered; batch not supported)] (25, 'ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/products/epaal-rugged-and-tough-bracelet-silicone-replacement-strap-for-xiaomi-mi-band-5#judgeme_product_reviews', 1, 'products', 'catalogue', 'catalogue', 1, 0, '2025-07-28T00:54:02.096212Z', 'default')
2025-07-28 00:54:02,099 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 16/16 (ordered; batch not supported)] (25, 'ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42', 'https://www.epaal.in/products/epaal-rugged-and-tough-bracelet-silicone-replacement-strap-for-xiaomi-mi-band-5#judgeme_product_reviews', 1, 'products', 'catalogue', 'catalogue', 1, 0, '2025-07-28T00:54:02.096212Z', 'default')
2025-07-28 00:54:02,100 INFO sqlalchemy.engine.Engine COMMIT
2025-07-28 00:54:02,100 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-28 00:54:02,113 - app.routers.mcc_analysis - INFO - MCC analysis task completed with status: COMPLETED
[2025-07-28 00:54:02][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: MCC URL classification results saved successfully
{
  "analysis_id": 25,
  "urls_saved": 16
}
[2025-07-28 00:54:02][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: MCC URL classification results saved successfully
[2025-07-28 00:54:02][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: Step 4 completed in 0.33 seconds
[2025-07-28 00:54:02][ba7a01ea-1845-4eca-b9c2-4cb0f805sdf0fasdf42][NO_REF] INFO: MCC analysis completed successfully
{
  "analysis_id": 25,
  "processing_time": "245.11s",
  "mcc": [
    "5311"
  ]
}
2025-07-28 00:54:02,113 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-28 00:54:02,113 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 00:54:02,113 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id AS mcc_analysis_gemini_id, mcc_analysis_gemini.website AS mcc_analysis_gemini_website, mcc_analysis_gemini.scrape_request_ref_id AS mcc_analysis_gemini_scrape_request_ref_id, mcc_analysis_gemini.result_status AS mcc_analysis_gemini_result_status, mcc_analysis_gemini.mcc_code AS mcc_analysis_gemini_mcc_code, mcc_analysis_gemini.business_category AS mcc_analysis_gemini_business_category, mcc_analysis_gemini.business_description AS mcc_analysis_gemini_business_description, mcc_analysis_gemini.reasoning AS mcc_analysis_gemini_reasoning, mcc_analysis_gemini.created_at AS mcc_analysis_gemini_created_at, mcc_analysis_gemini.started_at AS mcc_analysis_gemini_started_at, mcc_analysis_gemini.completed_at AS mcc_analysis_gemini_completed_at, mcc_analysis_gemini.failed_at AS mcc_analysis_gemini_failed_at, mcc_analysis_gemini.last_updated AS mcc_analysis_gemini_last_updated, mcc_analysis_gemini.error_message AS mcc_analysis_gemini_error_message, mcc_analysis_gemini.details AS mcc_analysis_gemini_details, mcc_analysis_gemini.org_id AS mcc_analysis_gemini_org_id, mcc_analysis_gemini.processing_status AS mcc_analysis_gemini_processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.id = ?
2025-07-28 00:54:02,113 - sqlalchemy.engine.Engine - INFO - SELECT mcc_analysis_gemini.id AS mcc_analysis_gemini_id, mcc_analysis_gemini.website AS mcc_analysis_gemini_website, mcc_analysis_gemini.scrape_request_ref_id AS mcc_analysis_gemini_scrape_request_ref_id, mcc_analysis_gemini.result_status AS mcc_analysis_gemini_result_status, mcc_analysis_gemini.mcc_code AS mcc_analysis_gemini_mcc_code, mcc_analysis_gemini.business_category AS mcc_analysis_gemini_business_category, mcc_analysis_gemini.business_description AS mcc_analysis_gemini_business_description, mcc_analysis_gemini.reasoning AS mcc_analysis_gemini_reasoning, mcc_analysis_gemini.created_at AS mcc_analysis_gemini_created_at, mcc_analysis_gemini.started_at AS mcc_analysis_gemini_started_at, mcc_analysis_gemini.completed_at AS mcc_analysis_gemini_completed_at, mcc_analysis_gemini.failed_at AS mcc_analysis_gemini_failed_at, mcc_analysis_gemini.last_updated AS mcc_analysis_gemini_last_updated, mcc_analysis_gemini.error_message AS mcc_analysis_gemini_error_message, mcc_analysis_gemini.details AS mcc_analysis_gemini_details, mcc_analysis_gemini.org_id AS mcc_analysis_gemini_org_id, mcc_analysis_gemini.processing_status AS mcc_analysis_gemini_processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.id = ?
2025-07-28 00:54:02,113 INFO sqlalchemy.engine.Engine [cached since 245.1s ago] (25,)
2025-07-28 00:54:02,113 - sqlalchemy.engine.Engine - INFO - [cached since 245.1s ago] (25,)
2025-07-28 00:54:02,114 INFO sqlalchemy.engine.Engine UPDATE mcc_analysis_gemini SET completed_at=? WHERE mcc_analysis_gemini.id = ?
2025-07-28 00:54:02,114 - sqlalchemy.engine.Engine - INFO - UPDATE mcc_analysis_gemini SET completed_at=? WHERE mcc_analysis_gemini.id = ?
2025-07-28 00:54:02,114 INFO sqlalchemy.engine.Engine [generated in 0.00012s] ('2025-07-28T00:54:02.113993Z', 25)
2025-07-28 00:54:02,114 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ('2025-07-28T00:54:02.113993Z', 25)
2025-07-28 00:54:02,114 INFO sqlalchemy.engine.Engine COMMIT
2025-07-28 00:54:02,114 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-28 00:55:21,741 - google_genai.models - INFO - AFC is enabled with max remote calls: 20000.
2025-07-28 00:55:26,666 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent "HTTP/1.1 200 OK"
2025-07-28 00:55:26,668 - google_genai.models - INFO - AFC remote call 1 is done.
[2025-07-28 00:54:20][text_extraction][NO_REF] ERROR: Failed to navigate to URL with proxy for text extraction (timeout)
{
  "error": "{'error': {'error': 'Page.goto: Timeout 36000ms exceeded.\\nCall log:\\n  - navigating to \"https://bakingcart.com/pages/about-us\", waiting until \"load\"\\n', 'url': 'https://bakingcart.com/pages/about-us', 'retry': 0}}",
  "error_type": "dict"
}
Traceback for analysis text_extraction:
Error printing traceback: 'dict' object has no attribute '__traceback__'
[2025-07-28 00:54:20][text_extraction][NO_REF] ERROR: Failed to access https://bakingcart.com/pages/about-us for text extraction with both direct and proxy connections
[2025-07-28 00:54:20][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] WARNING: ⚠️ Playwright extraction insufficient for about_us, trying requests method
[2025-07-28 00:54:20][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: ✅ Text extracted from about_us: 1868 characters
[2025-07-28 00:54:20][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: 📄 Extracting text from catalogue: https://bakingcart.com/
[2025-07-28 00:54:20][text_extraction][NO_REF] INFO: Starting text extraction
{
  "url": "https://bakingcart.com/",
  "timeout": 60
}
[2025-07-28 00:54:30][text_extraction][NO_REF] WARNING: JavaScript security challenge detected
{
  "url": "https://bakingcart.com/"
}
[2025-07-28 00:54:30][text_extraction][NO_REF] WARNING: Page appears to be blocked with direct connection
{
  "url": "https://bakingcart.com/",
  "retry": 0
}
[2025-07-28 00:54:30][text_extraction][NO_REF] INFO: Retrying direct connection in 1.50 seconds
[2025-07-28 00:54:40][text_extraction][NO_REF] WARNING: JavaScript security challenge detected
{
  "url": "https://bakingcart.com/"
}
[2025-07-28 00:54:40][text_extraction][NO_REF] WARNING: Page appears to be blocked with direct connection
{
  "url": "https://bakingcart.com/",
  "retry": 1
}
[2025-07-28 00:54:40][text_extraction][NO_REF] INFO: Attempting proxy connection for text extraction as fallback
[2025-07-28 00:54:40][text_extraction][NO_REF] INFO: Using webshare proxy with valid credentials
[2025-07-28 00:55:17][text_extraction][NO_REF] ERROR: Failed to navigate to URL with proxy for text extraction (timeout)
{
  "error": "{'error': {'error': 'Page.goto: Timeout 36000ms exceeded.\\nCall log:\\n  - navigating to \"https://bakingcart.com/\", waiting until \"load\"\\n', 'url': 'https://bakingcart.com/', 'retry': 0}}",
  "error_type": "dict"
}
Traceback for analysis text_extraction:
Error printing traceback: 'dict' object has no attribute '__traceback__'
[2025-07-28 00:55:17][text_extraction][NO_REF] ERROR: Failed to access https://bakingcart.com/ for text extraction with both direct and proxy connections
[2025-07-28 00:55:17][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] WARNING: ⚠️ Playwright extraction insufficient for catalogue, trying requests method
[2025-07-28 00:55:17][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: ✅ Text extracted from catalogue: 5000 characters
[2025-07-28 00:55:17][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: 📊 Text extraction completed: 3 categories with content
[2025-07-28 00:55:17][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Website information attempt 1
{
  "website": "https://bakingcart.com/"
}
[2025-07-28 00:55:17][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: 📊 URLs being passed to Gemini for website summary
{
  "total_urls_count": 11,
  "urls_breakdown_by_category": {
    "home_page": 1,
    "about_us": 1,
    "catalogue": 9
  },
  "priority_urls_structure": {
    "home_page": [
      "https://bakingcart.com/"
    ],
    "about_us": [
      "https://bakingcart.com/pages/about-us"
    ],
    "catalogue": [
      "https://bakingcart.com/",
      "https://bakingcart.com/collections/all-purpose-flour"
    ]
  },
  "max_urls_for_mcc_limit": 18,
  "extracted_content_categories": [
    "home_page",
    "about_us",
    "catalogue"
  ],
  "total_extracted_text_length": 11868
}
[2025-07-28 00:55:17][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: ✅ Using extracted text content for website analysis
[2025-07-28 00:55:17][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Calling Gemini API for website information...
[2025-07-28 00:55:21][mcc_analysis_unknown][NO_REF] INFO: Starting Gemini API call
{
  "model": "gemini-2.5-flash",
  "timeout": 90,
  "max_retries": 3,
  "prompt_length": 9442,
  "context": {
    "task_type": "mcc_analysis"
  }
}
[2025-07-28 00:55:21][mcc_analysis_unknown][NO_REF] INFO: Gemini API attempt 1/3
[2025-07-28 00:55:26][mcc_analysis_unknown][NO_REF] INFO: Gemini API Usage: cache_tokens_details=None cached_content_token_count=None candidates_token_count=340 candidates_tokens_details=None prompt_token_count=2701 prompt_tokens_details=[ModalityTokenCount(
  modality=<MediaModality.TEXT: 'TEXT'>,
  token_count=2701
)] thoughts_token_count=529 tool_use_prompt_token_count=None tool_use_prompt_tokens_details=None total_token_count=3570 traffic_type=None
[2025-07-28 00:55:26][mcc_analysis_unknown][NO_REF] INFO: Gemini API call successful
{
  "attempt": 1,
  "response_length": 1396,
  "finish_reason": "STOP"
}
[2025-07-28 00:55:31][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Website info response length: 1396
[2025-07-28 00:55:31][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Received Gemini API response for website information
[2025-07-28 00:55:31][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Successfully parsed JSON response
{
  "keys": [
    "is_valid_website",
    "website_redirection",
    "product_services",
    "line_of_business",
    "customers",
    "website_description"
  ]
}
[2025-07-28 00:55:31][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: 📋 WEBSITE SUMMARY OUTPUT FROM GEMINI
{
  "website": "https://bakingcart.com/",
  "total_urls_analyzed": 11,
  "summary_content": {
    "product_services": "Baking Cart offers a wide range of bakery ingredients, tools, and decorative items. Specific products include: Apyura Center Filling, Apyura Premium Spread (flavors like Orange Belgium, Tiramisu, Bubblegum, Rasmalai, Pistachio, Cookie and Crunch), FAB Oil Soluble (including FAB Butterscotch Oil Soluble Flavor), FAB Oil Soluble Premium, Silicone Moulds (e.g., Brownie Pan with Dividers, Star Cookie Cutter Set), Fab Premium Water Soluble, Fab Natural Flavours, Fab Vintage Vanilla Series, Fab Natural Extracts, Decorative Items, Bakery Essentials, Van Houten Cocoa Powder, Van Houten Dark Chocolate Couverture, Van Houten Milk Chocolate Couverture, LADY FINGER TIRAMISU, WILTON 1 M, 101 PIPING NOZZLE, Premium Stainless Steel Ice Cream Scoop with Trigger, and Al Barakah Premium Arabian Date Powder.",
    "line_of_business": "Bakery Ingredient Retailers, wholesale, exporter, and importer.",
    "customers": "The business serves both B2B (wholesalers, exporters, importers) and B2C (individual customers purchasing through the online cart) segments.",
    "website_description": "Baking Cart is an online venture by Madhav Enterprises, established in 2014, operating as a retailer, wholesaler, exporter, and importer of a diverse range of bakery ingredients, tools, and decorative items."
  },
  "summary_lengths": {
    "product_services_chars": 800,
    "line_of_business_chars": 63,
    "customers_chars": 140,
    "website_description_chars": 207
  }
}
[2025-07-28 00:55:31][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Website information extracted successfully
{
  "info_keys": [
    "product_services",
    "line_of_business",
    "customers",
    "website_description"
  ]
}
[2025-07-28 00:55:31][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: MCC classification attempt 1
{
  "website": "https://bakingcart.com/"
}
[2025-07-28 00:55:31][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Loading CSV data files...
[2025-07-28 00:55:31][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Loaded special_mccs.csv with 57 rows
[2025-07-28 00:55:31][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Loaded combined_mcc.csv with 303 rows2025-07-28 00:55:33,701 - google_genai.models - INFO - AFC is enabled with max remote calls: 20000.
2025-07-28 00:56:01,501 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent "HTTP/1.1 200 OK"
2025-07-28 00:56:01,509 - google_genai.models - INFO - AFC remote call 1 is done.

[2025-07-28 00:55:31][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: CSV data processing completed
[2025-07-28 00:55:31][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Calling Gemini API for MCC classification...
[2025-07-28 00:55:33][mcc_analysis_unknown][NO_REF] INFO: Starting Gemini API call
{
  "model": "gemini-2.5-flash",
  "timeout": 90,
  "max_retries": 3,
  "prompt_length": 223309,
  "context": {
    "task_type": "mcc_analysis"
  }
}
[2025-07-28 00:55:33][mcc_analysis_unknown][NO_REF] INFO: Gemini API attempt 1/3
[2025-07-28 00:56:01][mcc_analysis_unknown][NO_REF] INFO: Gemini API Usage: cache_tokens_details=None cached_content_token_count=None candidates_token_count=212 candidates_tokens_details=None prompt_token_count=46717 prompt_tokens_details=[ModalityTokenCount(
  modality=<MediaModality.TEXT: 'TEXT'>,
  token_count=46717
)] thoughts_token_count=5022 tool_use_prompt_token_count=None tool_use_prompt_tokens_details=None total_token_count=51951 traffic_type=None
[2025-07-28 00:56:01][mcc_analysis_unknown][NO_REF] INFO: Gemini API call successful
{
  "attempt": 1,
  "response_length": 900,
  "finish_reason": "STOP"
}
[2025-07-28 00:56:06][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Received Gemini API response for MCC classification
[2025-07-28 00:56:06][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Successfully parsed JSON response
{
  "keys": [
    "mcc",
    "business_desc",
    "business_category",
    "reason",
    "website"
  ]
}
[2025-07-28 00:56:06][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: MCC classification retrieved successfully
{
  "mcc": "5970",
  "business_desc": "Baking Cart is an online retailer, wholesaler, exporter, and importer specializing in a diverse range of bakery ingredients, tools, and decorative items for baking.",
  "business_category": "Artist Supply Stores, Craft Shops",
  "reason": "The business, 'Baking Cart,' primarily sells bakery ingredients, tools, and decorative items. While 'bakery ingredients' are food items, the overall nature of the business, including 'tools' and 'decorative items,' aligns with the concept of a 'Craft Shop.' MCC 5970, 'Artist Supply Stores, Craft Shops,' is the most specific and appropriate match as its description includes 'materials, equipment, and supplies used to create art, crafts, or other items,' and explicitly lists 'candy for candy-making' as an included product, which is analogous to bakery ingredients used in the craft of baking."
}
[2025-07-28 00:56:06][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Step 3: MCC classification completed in 216.62 seconds
{
  "website_info_available": true,
  "mcc_info_available": true,
  "processing_time": "216.62s"
}
[2025-07-28 00:56:06][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: ✅ Website content appears valid - continuing with normal flow
{
  "inactive_fields": 0,
  "valid_fields": 4,
  "threshold_check": "passed"
}
[2025-07-28 00:56:06][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Step 4: Saving results to database
[2025-07-28 00:56:06][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Saving MCC analysis results to database
{
  "website": "https://bakingcart.com/"
}
2025-07-28 00:56:06,512 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = ?
2025-07-28 00:56:06,512 - sqlalchemy.engine.Engine - INFO - SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = ?
2025-07-28 00:56:06,512 INFO sqlalchemy.engine.Engine [cached since 370.9s ago] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74',)
2025-07-28 00:56:06,512 - sqlalchemy.engine.Engine - INFO - [cached since 370.9s ago] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74',)
2025-07-28 00:56:06,512 INFO sqlalchemy.engine.Engine UPDATE mcc_analysis_gemini SET mcc_code=?, business_category=?, business_description=?, reasoning=?, completed_at=?, details=?, processing_status=? WHERE mcc_analysis_gemini.id = ?
2025-07-28 00:56:06,512 - sqlalchemy.engine.Engine - INFO - UPDATE mcc_analysis_gemini SET mcc_code=?, business_category=?, business_description=?, reasoning=?, completed_at=?, details=?, processing_status=? WHERE mcc_analysis_gemini.id = ?
2025-07-28 00:56:06,512 INFO sqlalchemy.engine.Engine [cached since 124.7s ago] ('5970', 'Artist Supply Stores, Craft Shops', '', "The business, 'Baking Cart,' primarily sells bakery ingredients, tools, and decorative items. While 'bakery ingredients' are food items, the overall  ... (298 characters truncated) ... r items,' and explicitly lists 'candy for candy-making' as an included product, which is analogous to bakery ingredients used in the craft of baking.", '2025-07-28T00:56:06.512489Z', '{"website": "https://bakingcart.com/", "scrape_request_ref_id": "b07e8022-c849-4042-a353-c99216bbasdfasdf5d74", "website_info": {"product_services":  ... (3375 characters truncated) ... lysis", "content_availability_status": "content_available", "fallback_method_used": false, "text_extraction_used": false, "insufficient_data": false}', 'COMPLETED', 26)
2025-07-28 00:56:06,512 - sqlalchemy.engine.Engine - INFO - [cached since 124.7s ago] ('5970', 'Artist Supply Stores, Craft Shops', '', "The business, 'Baking Cart,' primarily sells bakery ingredients, tools, and decorative items. While 'bakery ingredients' are food items, the overall  ... (298 characters truncated) ... r items,' and explicitly lists 'candy for candy-making' as an included product, which is analogous to bakery ingredients used in the craft of baking.", '2025-07-28T00:56:06.512489Z', '{"website": "https://bakingcart.com/", "scrape_request_ref_id": "b07e8022-c849-4042-a353-c99216bbasdfasdf5d74", "website_info": {"product_services":  ... (3375 characters truncated) ... lysis", "content_availability_status": "content_available", "fallback_method_used": false, "text_extraction_used": false, "insufficient_data": false}', 'COMPLETED', 26)
2025-07-28 00:56:06,513 INFO sqlalchemy.engine.Engine COMMIT
2025-07-28 00:56:06,513 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-28 00:56:06,784 - httpx - INFO - HTTP Request: PATCH https://bffapi.biztel.ai/api/mcc/results "HTTP/1.1 401 "
[2025-07-28 00:56:06][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: MCC analysis results saved successfully
{
  "analysis_id": 26
}
[2025-07-28 00:56:06][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: 🚀 Preparing MCC results webhook with correct format
{
  "scrape_request_ref_id": "b07e8022-c849-4042-a353-c99216bbasdfasdf5d74",
  "website": "https://bakingcart.com/",
  "analysis_id": 26
}
[2025-07-28 00:56:06][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: 📦 MCC WEBHOOK PAYLOAD CONSTRUCTION - STEP BY STEP
{
  "construction_step": "1_mcc_payload_created",
  "timestamp": "2025-07-28T00:56:06.525304",
  "website": "https://bakingcart.com/",
  "scrape_request_ref_id": "b07e8022-c849-4042-a353-c99216bbasdfasdf5d74",
  "mcc_code": "5970",
  "business_category": "Artist Supply Stores, Craft Shops",
  "business_description_length": 0,
  "payload_keys": [
    "website",
    "createdDate",
    "status",
    "scrapeRequestUuid",
    "mcc",
    "manualMcc",
    "businessCategory",
    "businessDescription"
  ],
  "payload_structure": {
    "website": "https://bakingcart.com/",
    "scrapeRequestUuid": "b07e8022-c849-4042-a353-c99216bbasdfasdf5d74",
    "status": "COMPLETED",
    "mcc": 5970,
    "manualMcc": -1,
    "businessCategory": "Artist Supply Stores, Craft Shops",
    "businessDescription_length": 0,
    "createdDate": "2025-07-28T00:56:06.525294Z"
  },
  "complete_payload_json": {
    "website": "https://bakingcart.com/",
    "createdDate": "2025-07-28T00:56:06.525294Z",
    "status": "COMPLETED",
    "scrapeRequestUuid": "b07e8022-c849-4042-a353-c99216bbasdfasdf5d74",
    "mcc": 5970,
    "manualMcc": -1,
    "businessCategory": "Artist Supply Stores, Craft Shops",
    "businessDescription": ""
  }
}
[2025-07-28 00:56:06][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: 📤 Sending MCC PATCH request with clean headers - DETAILED DEBUG
{
  "url": "https://bffapi.biztel.ai/api/mcc/results",
  "method": "PATCH",
  "payload": {
    "website": "https://bakingcart.com/",
    "createdDate": "2025-07-28T00:56:06.525294Z",
    "status": "COMPLETED",
    "scrapeRequestUuid": "b07e8022-c849-4042-a353-c99216bbasdfasdf5d74",
    "mcc": 5970,
    "manualMcc": -1,
    "businessCategory": "Artist Supply Stores, Craft Shops",
    "businessDescription": ""
  },
  "headers_being_sent": {
    "X-API-KEY": "12345678",
    "Content-Type": "application/json"
  },
  "api_key_in_headers": "12345678",
  "content_type_in_headers": "application/json",
  "api_key_length": 8
}
[2025-07-28 00:56:06][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: 🔥 MCC PATCH REQUEST - FINAL TRANSMISSION
{
  "construction_step": "2_sending_mcc_request",
  "timestamp": "2025-07-28T00:56:06.530181",
  "about_to_send": true,
  "final_url": "https://bffapi.biztel.ai/api/mcc/results",
  "final_method": "PATCH",
  "final_headers": {
    "X-API-KEY": "12345678",
    "Content-Type": "application/json"
  },
  "final_payload": {
    "website": "https://bakingcart.com/",
    "createdDate": "2025-07-28T00:56:06.525294Z",
    "status": "COMPLETED",
    "scrapeRequestUuid": "b07e8022-c849-4042-a353-c99216bbasdfasdf5d74",
    "mcc": 5970,
    "manualMcc": -1,
    "businessCategory": "Artist Supply Stores, Craft Shops",
    "businessDescription": ""
  },
  "final_timeout": 30.0,
  "curl_equivalent": "curl -X PATCH 'https://bffapi.biztel.ai/api/mcc/results' -H 'Content-Type: application/json' -H 'X-API-KEY: 12345678' -d '{\"website\": \"https://bakingcart.com/\", \"createdDate\": \"2025-07-28T00:56:06.525294Z\", \"status\": \"COMPLETED\", \"scrapeRequestUuid\": \"b07e8022-c849-4042-a353-c99216bbasdfasdf5d74\", \"mcc\": 5970, \"manualMcc\": -1, \"businessCategory\": \"Artist Supply Stores, Craft Shops\", \"businessDescription\": \"\"}'"
}
[2025-07-28 00:56:06][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: ⚡ MCC PATCH RESPONSE - IMMEDIATE RESULT
{
  "construction_step": "3_mcc_response_received",
  "timestamp": "2025-07-28T00:56:06.784593",
  "response_status_code": 401,
  "response_headers": {
    "server": "nginx/1.22.1",
    "date": "Sun, 27 Jul 2025 19:26:06 GMT",
    "content-length": "0",
    "connection": "keep-alive",
    "vary": "Origin, Access-Control-Request-Method, Access-Control-Request-Headers",
    "x-content-type-options": "nosniff",
    "x-xss-protection": "0",
    "cache-control": "no-cache, no-store, max-age=0, must-revalidate",
    "pragma": "no-cache",
    "expires": "0",
    "x-frame-options": "DENY",
    "www-authenticate": "Basic realm=\"Realm\""
  },
  "response_text": "",
  "response_success": false,
  "response_size": 0,
  "request_url": "https://bffapi.biztel.ai/api/mcc/results"
}
[2025-07-28 00:56:06][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: 📥 MCC PATCH response received
{
  "status_code": 401,
  "response_text": "",
  "response_headers": {
    "server": "nginx/1.22.1",
    "date": "Sun, 27 Jul 2025 19:26:06 GMT",
    "content-length": "0",
    "connection": "keep-alive",
    "vary": "Origin, Access-Control-Request-Method, Access-Control-Request-Headers",
    "x-content-type-options": "nosniff",
    "x-xss-protection": "0",
    "cache-control": "no-cache, no-store, max-age=0, must-revalidate",
    "pragma": "no-cache",
    "expires": "0",
    "x-frame-options": "DENY",
    "www-authenticate": "Basic realm=\"Realm\""
  },
  "url": "https://bffapi.biztel.ai/api/mcc/results"
}
[2025-07-28 00:56:06][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] ERROR: ❌ MCC results webhook failed - Authentication error
{
  "error": "{'status_code': 401, 'response': '', 'url': 'https://bffapi.biztel.ai/api/mcc/results', 'error': 'Invalid API key - check BIZTEL_API_KEY in .env file', 'api_key_length': 8}",
  "error_type": "dict"
}
Traceback for analysis b07e8022-c849-4042-a353-c99216bbasdfasdf5d74:
Error printing traceback: 'dict' object has no attribute '__traceback__'
[2025-07-28 00:56:06][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: 🔄 Attempting to send default values despite authentication error
[2025-07-28 00:56:06][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: 📋 Created default payload for authentication error
{
  "payload": {
    "website": "https://bakingcart.com/",
    "createdDate": "2025-07-28T00:56:06.784737Z",
    "status": "FAILED",
    "scrapeRequestUuid": "b07e8022-c849-4042-a353-c99216bbasdfasdf5d74",
    "mcc": -1,
    "manualMcc": -1,
    "businessCategory": "authentication_error",
    "businessDescription": "webhook authentication failed"
  }
}
[2025-07-28 00:56:06][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] WARNING: ⚠️ MCC results webhook failed
{
  "analysis_id": 26
}
[2025-07-28 00:56:06][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Saving detailed MCC URL classification results
[2025-07-28 00:56:06][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Saving MCC URL classification results
{
  "analysis_id": 26,
  "soft_categories": 16,
  "hard_categories": 10
}
2025-07-28 00:56:06,785 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-28 00:56:06,785 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 00:56:06,785 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-28 00:56:06,785 - sqlalchemy.engine.Engine - INFO - SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-28 00:56:06,785 INFO sqlalchemy.engine.Engine [cached since 371.1s ago] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74',)
2025-07-28 00:56:06,785 - sqlalchemy.engine.Engine - INFO - [cached since 371.1s ago] ('b07e8022-c849-4042-a353-c99216bbasdfasdf5d74',)
2025-07-28 00:56:06,791 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:56:06,791 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:56:06,791 INFO sqlalchemy.engine.Engine [cached since 124.7s ago (insertmanyvalues) 1/14 (ordered; batch not supported)] (26, 'b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 1, 'home_page', 'home_page,catalogue', 'home_page,catalogue', 1, 1, '2025-07-28T00:56:06.789864Z', 'default')
2025-07-28 00:56:06,791 - sqlalchemy.engine.Engine - INFO - [cached since 124.7s ago (insertmanyvalues) 1/14 (ordered; batch not supported)] (26, 'b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/', 1, 'home_page', 'home_page,catalogue', 'home_page,catalogue', 1, 1, '2025-07-28T00:56:06.789864Z', 'default')
2025-07-28 00:56:06,791 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:56:06,791 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:56:06,791 INFO sqlalchemy.engine.Engine [insertmanyvalues 2/14 (ordered; batch not supported)] (26, 'b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/collections/all-purpose-flour', 1, 'services', 'catalogue', 'catalogue', 1, 0, '2025-07-28T00:56:06.790012Z', 'default')
2025-07-28 00:56:06,791 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 2/14 (ordered; batch not supported)] (26, 'b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/collections/all-purpose-flour', 1, 'services', 'catalogue', 'catalogue', 1, 0, '2025-07-28T00:56:06.790012Z', 'default')
2025-07-28 00:56:06,791 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:56:06,791 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:56:06,791 INFO sqlalchemy.engine.Engine [insertmanyvalues 3/14 (ordered; batch not supported)] (26, 'b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-rose', 1, 'products', 'Unreachable_via_tool', 'Unreachable_via_tool', 0, 0, '2025-07-28T00:56:06.790106Z', 'default')
2025-07-28 00:56:06,791 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 3/14 (ordered; batch not supported)] (26, 'b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/collections/fab-oil-soluble/products/oil-soluble-rose', 1, 'products', 'Unreachable_via_tool', 'Unreachable_via_tool', 0, 0, '2025-07-28T00:56:06.790106Z', 'default')
2025-07-28 00:56:06,791 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:56:06,791 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:56:06,791 INFO sqlalchemy.engine.Engine [insertmanyvalues 4/14 (ordered; batch not supported)] (26, 'b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/pages/contact', 1, 'contact_us', 'contact_us', 'contact_us', 0, 0, '2025-07-28T00:56:06.790186Z', 'default')
2025-07-28 00:56:06,791 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 4/14 (ordered; batch not supported)] (26, 'b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/pages/contact', 1, 'contact_us', 'contact_us', 'contact_us', 0, 0, '2025-07-28T00:56:06.790186Z', 'default')
2025-07-28 00:56:06,792 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:56:06,792 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:56:06,792 INFO sqlalchemy.engine.Engine [insertmanyvalues 5/14 (ordered; batch not supported)] (26, 'b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/collections/fab-natural-extracts', 1, 'services', 'catalogue', 'catalogue', 1, 0, '2025-07-28T00:56:06.790262Z', 'default')
2025-07-28 00:56:06,792 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 5/14 (ordered; batch not supported)] (26, 'b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/collections/fab-natural-extracts', 1, 'services', 'catalogue', 'catalogue', 1, 0, '2025-07-28T00:56:06.790262Z', 'default')
2025-07-28 00:56:06,792 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:56:06,792 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:56:06,792 INFO sqlalchemy.engine.Engine [insertmanyvalues 6/14 (ordered; batch not supported)] (26, 'b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/pages/about-us', 1, 'about_us', 'about_us', 'about_us', 1, 0, '2025-07-28T00:56:06.790338Z', 'default')
2025-07-28 00:56:06,792 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 6/14 (ordered; batch not supported)] (26, 'b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/pages/about-us', 1, 'about_us', 'about_us', 'about_us', 1, 0, '2025-07-28T00:56:06.790338Z', 'default')
2025-07-28 00:56:06,792 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:56:06,792 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:56:06,792 INFO sqlalchemy.engine.Engine [insertmanyvalues 7/14 (ordered; batch not supported)] (26, 'b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/collections/all', 1, 'catalogue', 'catalogue', 'catalogue', 1, 0, '2025-07-28T00:56:06.790415Z', 'default')
2025-07-28 00:56:06,792 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 7/14 (ordered; batch not supported)] (26, 'b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/collections/all', 1, 'catalogue', 'catalogue', 'catalogue', 1, 0, '2025-07-28T00:56:06.790415Z', 'default')
2025-07-28 00:56:06,792 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:56:06,792 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:56:06,792 INFO sqlalchemy.engine.Engine [insertmanyvalues 8/14 (ordered; batch not supported)] (26, 'b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/products/101-piping-nozzle', 1, 'products', 'catalogue', 'catalogue', 1, 0, '2025-07-28T00:56:06.790515Z', 'default')
2025-07-28 00:56:06,792 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 8/14 (ordered; batch not supported)] (26, 'b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/products/101-piping-nozzle', 1, 'products', 'catalogue', 'catalogue', 1, 0, '2025-07-28T00:56:06.790515Z', 'default')
2025-07-28 00:56:06,792 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:56:06,792 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:56:06,792 INFO sqlalchemy.engine.Engine [insertmanyvalues 9/14 (ordered; batch not supported)] (26, 'b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/collections/apyura-premium-spread', 1, 'services', 'catalogue', 'catalogue', 1, 0, '2025-07-28T00:56:06.790608Z', 'default')
2025-07-28 00:56:06,792 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 9/14 (ordered; batch not supported)] (26, 'b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/collections/apyura-premium-spread', 1, 'services', 'catalogue', 'catalogue', 1, 0, '2025-07-28T00:56:06.790608Z', 'default')
2025-07-28 00:56:06,792 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:56:06,792 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:56:06,792 INFO sqlalchemy.engine.Engine [insertmanyvalues 10/14 (ordered; batch not supported)] (26, 'b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/collections', 1, 'services', 'catalogue', 'catalogue', 1, 0, '2025-07-28T00:56:06.790688Z', 'default')
2025-07-28 00:56:06,792 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 10/14 (ordered; batch not supported)] (26, 'b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/collections', 1, 'services', 'catalogue', 'catalogue', 1, 0, '2025-07-28T00:56:06.790688Z', 'default')
2025-07-28 00:56:06,792 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:56:06,792 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:56:06,792 INFO sqlalchemy.engine.Engine [insertmanyvalues 11/14 (ordered; batch not supported)] (26, 'b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/products/al-barakah-premium-arabian-date-powder-250gram-date-sugar-natural-sweetener-perfect-for-desserts-baking-and-smoothie-sugar-substitute', 1, 'products', 'Unreachable_via_tool', 'Unreachable_via_tool', 0, 0, '2025-07-28T00:56:06.790763Z', 'default')
2025-07-28 00:56:06,792 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 11/14 (ordered; batch not supported)] (26, 'b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/products/al-barakah-premium-arabian-date-powder-250gram-date-sugar-natural-sweetener-perfect-for-desserts-baking-and-smoothie-sugar-substitute', 1, 'products', 'Unreachable_via_tool', 'Unreachable_via_tool', 0, 0, '2025-07-28T00:56:06.790763Z', 'default')
2025-07-28 00:56:06,792 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:56:06,792 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:56:06,792 INFO sqlalchemy.engine.Engine [insertmanyvalues 12/14 (ordered; batch not supported)] (26, 'b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/collections/all-collections', 1, 'services', 'catalogue', 'catalogue', 1, 0, '2025-07-28T00:56:06.790835Z', 'default')
2025-07-28 00:56:06,792 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 12/14 (ordered; batch not supported)] (26, 'b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/collections/all-collections', 1, 'services', 'catalogue', 'catalogue', 1, 0, '2025-07-28T00:56:06.790835Z', 'default')
2025-07-28 00:56:06,792 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:56:06,792 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:56:06,792 INFO sqlalchemy.engine.Engine [insertmanyvalues 13/14 (ordered; batch not supported)] (26, 'b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/oil-soluble-passion-fruit', 1, 'products', 'Unreachable_via_tool', 'Unreachable_via_tool', 0, 0, '2025-07-28T00:56:06.790907Z', 'default')
2025-07-28 00:56:06,792 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 13/14 (ordered; batch not supported)] (26, 'b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/oil-soluble-passion-fruit', 1, 'products', 'Unreachable_via_tool', 'Unreachable_via_tool', 0, 0, '2025-07-28T00:56:06.790907Z', 'default')
2025-07-28 00:56:06,792 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:56:06,792 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-28 00:56:06,792 INFO sqlalchemy.engine.Engine [insertmanyvalues 14/14 (ordered; batch not supported)] (26, 'b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/products/titamisu', 1, 'products', 'catalogue', 'catalogue', 1, 0, '2025-07-28T00:56:06.790979Z', 'default')
2025-07-28 00:56:06,792 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 14/14 (ordered; batch not supported)] (26, 'b07e8022-c849-4042-a353-c99216bbasdfasdf5d74', 'https://bakingcart.com/products/titamisu', 1, 'products', 'catalogue', 'catalogue', 1, 0, '2025-07-28T00:56:06.790979Z', 'default')
2025-07-28 00:56:06,793 INFO sqlalchemy.engine.Engine COMMIT
2025-07-28 00:56:06,793 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-28 00:56:06,806 - app.routers.mcc_analysis - INFO - MCC analysis task completed with status: COMPLETED
[2025-07-28 00:56:06][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: MCC URL classification results saved successfully
{
  "analysis_id": 26,
  "urls_saved": 14
}
[2025-07-28 00:56:06][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: MCC URL classification results saved successfully
[2025-07-28 00:56:06][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: Step 4 completed in 0.29 seconds
[2025-07-28 00:56:06][b07e8022-c849-4042-a353-c99216bbasdfasdf5d74][NO_REF] INFO: MCC analysis completed successfully
{
  "analysis_id": 26,
  "processing_time": "241.27s",
  "mcc": [
    "5970"
  ]
}
2025-07-28 00:56:06,806 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-28 00:56:06,806 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-28 00:56:06,807 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id AS mcc_analysis_gemini_id, mcc_analysis_gemini.website AS mcc_analysis_gemini_website, mcc_analysis_gemini.scrape_request_ref_id AS mcc_analysis_gemini_scrape_request_ref_id, mcc_analysis_gemini.result_status AS mcc_analysis_gemini_result_status, mcc_analysis_gemini.mcc_code AS mcc_analysis_gemini_mcc_code, mcc_analysis_gemini.business_category AS mcc_analysis_gemini_business_category, mcc_analysis_gemini.business_description AS mcc_analysis_gemini_business_description, mcc_analysis_gemini.reasoning AS mcc_analysis_gemini_reasoning, mcc_analysis_gemini.created_at AS mcc_analysis_gemini_created_at, mcc_analysis_gemini.started_at AS mcc_analysis_gemini_started_at, mcc_analysis_gemini.completed_at AS mcc_analysis_gemini_completed_at, mcc_analysis_gemini.failed_at AS mcc_analysis_gemini_failed_at, mcc_analysis_gemini.last_updated AS mcc_analysis_gemini_last_updated, mcc_analysis_gemini.error_message AS mcc_analysis_gemini_error_message, mcc_analysis_gemini.details AS mcc_analysis_gemini_details, mcc_analysis_gemini.org_id AS mcc_analysis_gemini_org_id, mcc_analysis_gemini.processing_status AS mcc_analysis_gemini_processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.id = ?
2025-07-28 00:56:06,807 - sqlalchemy.engine.Engine - INFO - SELECT mcc_analysis_gemini.id AS mcc_analysis_gemini_id, mcc_analysis_gemini.website AS mcc_analysis_gemini_website, mcc_analysis_gemini.scrape_request_ref_id AS mcc_analysis_gemini_scrape_request_ref_id, mcc_analysis_gemini.result_status AS mcc_analysis_gemini_result_status, mcc_analysis_gemini.mcc_code AS mcc_analysis_gemini_mcc_code, mcc_analysis_gemini.business_category AS mcc_analysis_gemini_business_category, mcc_analysis_gemini.business_description AS mcc_analysis_gemini_business_description, mcc_analysis_gemini.reasoning AS mcc_analysis_gemini_reasoning, mcc_analysis_gemini.created_at AS mcc_analysis_gemini_created_at, mcc_analysis_gemini.started_at AS mcc_analysis_gemini_started_at, mcc_analysis_gemini.completed_at AS mcc_analysis_gemini_completed_at, mcc_analysis_gemini.failed_at AS mcc_analysis_gemini_failed_at, mcc_analysis_gemini.last_updated AS mcc_analysis_gemini_last_updated, mcc_analysis_gemini.error_message AS mcc_analysis_gemini_error_message, mcc_analysis_gemini.details AS mcc_analysis_gemini_details, mcc_analysis_gemini.org_id AS mcc_analysis_gemini_org_id, mcc_analysis_gemini.processing_status AS mcc_analysis_gemini_processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.id = ?
2025-07-28 00:56:06,807 INFO sqlalchemy.engine.Engine [cached since 369.8s ago] (26,)
2025-07-28 00:56:06,807 - sqlalchemy.engine.Engine - INFO - [cached since 369.8s ago] (26,)
2025-07-28 00:56:06,807 INFO sqlalchemy.engine.Engine UPDATE mcc_analysis_gemini SET completed_at=? WHERE mcc_analysis_gemini.id = ?
2025-07-28 00:56:06,807 - sqlalchemy.engine.Engine - INFO - UPDATE mcc_analysis_gemini SET completed_at=? WHERE mcc_analysis_gemini.id = ?
2025-07-28 00:56:06,807 INFO sqlalchemy.engine.Engine [cached since 124.7s ago] ('2025-07-28T00:56:06.807322Z', 26)
2025-07-28 00:56:06,807 - sqlalchemy.engine.Engine - INFO - [cached since 124.7s ago] ('2025-07-28T00:56:06.807322Z', 26)
2025-07-28 00:56:06,807 INFO sqlalchemy.engine.Engine COMMIT
2025-07-28 00:56:06,807 - sqlalchemy.engine.Engine - INFO - COMMIT
