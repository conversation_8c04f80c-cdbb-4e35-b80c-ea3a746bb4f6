# CI/CD Pipeline Documentation

## Overview

This repository uses GitHub Actions for continuous integration and deployment. The pipeline includes automated testing, security scanning, building, and deployment to Google Cloud Run.

## Pipeline Structure

### 1. Main CI/CD Pipeline (`.github/workflows/ci-cd-pipeline.yml`)

**Triggers:**
- Push to `main`, `mcc-only`, or `my-local-changes` branches
- Pull requests to `main` or `mcc-only` branches
- Excludes documentation changes

**Stages:**

#### 🔍 **Lint and Test**
- Code linting with `flake8`
- Code formatting check with `black`
- Import sorting check with `isort`
- Unit tests with `pytest`
- Coverage reporting

#### 🔒 **Security Scan**
- Security vulnerability scanning with `bandit`
- Dependency vulnerability check with `safety`
- Artifact upload for security reports

#### 🐳 **Build and Test Docker Image**
- Docker image building and pushing to GitHub Container Registry
- Image testing and health checks
- Caching for faster builds

#### 🚀 **Deploy to Staging**
- Automatic deployment to staging environment (main branch only)
- Smoke tests after deployment
- Environment-specific configuration

#### 🎯 **Deploy to Production**
- Automatic deployment to production environment (main branch only)
- Comprehensive testing after deployment
- Production-specific configuration

#### 📢 **Failure Notifications**
- Automated notifications on pipeline failures
- Configurable notification channels

### 2. Manual Deploy Workflow (`.github/workflows/manual-deploy.yml`)

**Triggers:**
- Manual workflow dispatch
- Release publication

**Features:**
- Environment selection (staging/production)
- Custom image tag support
- Manual approval gates
- Health checks and smoke tests

### 3. Dependency Check Workflow (`.github/workflows/dependency-check.yml`)

**Triggers:**
- Weekly scheduled runs (Mondays at 9 AM UTC)
- Manual dispatch
- Changes to dependency files

**Features:**
- Security vulnerability scanning
- Outdated package detection
- Automatic issue creation for vulnerabilities
- Code quality checks

## Required Secrets

Configure these secrets in your GitHub repository settings:

### Google Cloud
```bash
GOOGLE_CLOUD_PROJECT=your-project-id
GCP_SA_KEY=your-service-account-key
WORKLOAD_IDENTITY_PROVIDER=your-workload-identity-provider
SERVICE_ACCOUNT=your-service-account
```

### Database URLs
```bash
STAGING_DATABASE_URL=your-staging-database-url
PROD_DATABASE_URL=your-production-database-url
```

## Environment Configuration

### Staging Environment
- **Service Name**: `webreview-ds-api-staging`
- **Region**: `us-central1`
- **Auto-deploy**: On push to `main`

### Production Environment
- **Service Name**: `webreview-ds-api-prod`
- **Region**: `us-central1`
- **Auto-deploy**: On push to `main`

## Usage

### Automatic Deployment
1. Push to `main` branch
2. Pipeline automatically runs all stages
3. Deployment happens after successful tests

### Manual Deployment
1. Go to Actions tab in GitHub
2. Select "Manual Deploy" workflow
3. Click "Run workflow"
4. Choose environment and image tag
5. Click "Run workflow"

### Release Deployment
1. Create a new release in GitHub
2. Tag with version (e.g., `v1.0.0`)
3. Publish the release
4. Automatic deployment to production

## Testing

### Local Testing
```bash
# Install development dependencies
pip install -r requirements-dev.txt

# Run tests
pytest

# Run linting
flake8 app/
black --check app/
isort --check-only app/

# Run security scan
bandit -r app/
safety check
```

### Pipeline Testing
- Tests run automatically on every push/PR
- Coverage reports generated
- Security scans performed
- Docker image tested

## Monitoring

### Health Checks
- `/health` - Basic service health
- `/mcc-analysis/health` - MCC analysis service health
- `/error-notification/health` - Error notification service health

### Logs
- Google Cloud Logging integration
- Structured logging with correlation IDs
- Error tracking and monitoring

## Troubleshooting

### Common Issues

1. **Build Failures**
   - Check Dockerfile syntax
   - Verify requirements.txt
   - Check for missing dependencies

2. **Deployment Failures**
   - Verify Google Cloud credentials
   - Check service account permissions
   - Validate environment variables

3. **Test Failures**
   - Run tests locally first
   - Check test dependencies
   - Verify test data

### Debugging
- Check GitHub Actions logs
- Review artifact uploads
- Monitor Google Cloud Run logs
- Use manual deployment for testing

## Best Practices

1. **Branch Strategy**
   - Use feature branches for development
   - Merge to `main` for deployment
   - Use `my-local-changes` for experiments

2. **Testing**
   - Write tests for new features
   - Maintain good test coverage
   - Use integration tests for critical paths

3. **Security**
   - Regular dependency updates
   - Security scanning in pipeline
   - Follow least privilege principle

4. **Monitoring**
   - Set up alerts for failures
   - Monitor application metrics
   - Track deployment success rates

## Support

For issues with the CI/CD pipeline:
1. Check GitHub Actions logs
2. Review this documentation
3. Create an issue with detailed error information 