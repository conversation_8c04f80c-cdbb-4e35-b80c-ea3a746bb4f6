import pytest
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

def test_health_check():
    """Test the health check endpoint"""
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json() == {"status": "ok", "message": "Service is running"}

def test_mcc_analysis_health():
    """Test the MCC analysis health endpoint"""
    response = client.get("/mcc-analysis/health")
    assert response.status_code == 200

def test_error_notification_health():
    """Test the error notification health endpoint"""
    response = client.get("/error-notification/health")
    assert response.status_code == 200

def test_app_import():
    """Test that the app can be imported successfully"""
    import app
    assert app is not None 